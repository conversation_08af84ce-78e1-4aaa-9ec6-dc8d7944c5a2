import {
  app,
  BrowserWindow,
  globalShortcut,
  clipboard,
  screen,
} from 'electron';
import os from 'os';
import Path from 'path';
import fse from 'fs-extra';
import { execFile } from 'child_process';
import { mainIPC } from '@htElectronSDK/main/ipc';
import { ensureDir } from '@utils/folderUtils';
import { SUCCESS_CON } from '@common/constant';

let displays = [];
let captureWins = [];
let captureWinIds = [];
let hasHideWindow = false;
let isFocused = false;

const winURL =
  process.env.NODE_ENV === 'development'
    ? `http://localhost:9080`
    : `file://${__dirname}/index.html`;
const captureURL = `${winURL}#capture`;
const platform = os.platform();

const basepath =
  process.platform === 'win32'
    ? `${app.getPath('temp')}\\screen_shot\\`
    : `${os.homedir()}/.linkim_local/screen_shot/`;
let state = fse.existsSync(basepath);

const preCapture = () => {
  // 预创建screen_shot文件夹
  createFolder();
  initListern();
  createCaptureWindows();
};

const hideWins = () => {
  if (captureWins) {
    captureWins.forEach((win) => win.hide());
  }
};

/**
 * 创建截图窗口
 */
const createCaptureWindows = () => {
  if (captureWins.length) {
    console.warn('创建失败, 截图窗口已存在');
  }
  displays = getAllDisplays();
  captureWins = displays.map((display) => {
    let captureWin = new BrowserWindow({
      width: display.bounds.width,
      height: display.bounds.height,
      x: display.bounds.x,
      y: display.bounds.y,
      webPreferences: {
        contextIsolation: false,
        nodeIntegration: true,
        webviewTag: true,
        webSecurity: process.env.NODE_ENV === 'production',
      },
      show: false, // 先隐藏
      fullscreen: platform === 'win32' || undefined,
      resizable: false,
      enableLargerThanScreen: true,
      skipTaskbar: true,
      movable: false,
      frame: false,
      transparent: true,
      focusable: true,
    });
    captureWin.setAlwaysOnTop(true, 'screen-saver');
    captureWin.hide();
    captureWin.setVisibleOnAllWorkspaces(true);
    captureWin.setFullScreenable(true);
    captureWin.loadURL(captureURL);

    // 调试用
    captureWin.webContents.closeDevTools(); // 打开dev模式会白底不会透明
    captureWin.on('show', () => {
      globalShortcut.register('Esc', () => {
        reset();
        mainIPC.show();
      });
    });
    captureWin.on('closed', () => {
      globalShortcut.unregister('Esc');
      captureWin = null;
    });
    return captureWin;
  });
  initCaptureWinIds(captureWins); // 获取displays ids
};

/**
 * 绑定截图进程通讯的ipc
 */
const initListern = () => {
  if (platform === 'win32') {
    listenDisplayNumChange();
  }
};

/**
 * 监听屏幕数量变化. (开启扩展屏模式下)
 */
const listenDisplayNumChange = () => {
  screen.on('display-added', () => {
    reset();
  });

  screen.on('display-removed', () => {
    reset();
  });
};

/**
 * 启动截图
 */
const startScreenshot = (hideWindow: boolean) => {
  if (platform === 'darwin') {
  } else if (platform === 'win32') {
    hasHideWindow = hideWindow;
    isFocused = mainIPC.getMainWindowWebContents().isFocused();
    // window截图
    startWinScreenshot();
  }
};

const startWinScreenshot = () => {
  createFolder();

  let captureExePath = Path.join(
    process.resourcesPath,
    'capture/win32/PrintScr.exe'
  );
  if (process.env.NODE_ENV === 'development') {
    captureExePath = Path.resolve(
      __dirname,
      '../../capture/win32/PrintScr.exe'
    );
  }

  const screen_window = execFile(captureExePath);

  screen_window.on('error', (err) => {
    mainIPC
      .getMainWindowWebContents()
      .send('show-message-tip', 'error', '截图插件调用异常');
  });
  screen_window.on('exit', function (code) {
    // 执行成功返回 1，返回 0 没有截图
    // 得到路径，发给主窗口
    if (code === 1) {
      const nativeImage = clipboard.readImage();
      if (nativeImage && !nativeImage.isEmpty()) {
        // 确保目录存在，使用递归创建
        const dirResult = ensureDir(basepath);
        if (dirResult.code !== SUCCESS_CON) {
          console.error('创建截图目录失败:', dirResult.errMsg);
          return;
        }
        const p = Path.join(
          basepath,
          `/screen_shot_${new Date().getTime()}.png`
        );
        fse.writeFile(p, nativeImage.toPNG(), async (err) => {
          if (err) {
            reset();
          }
        });

        if (hasHideWindow || isFocused) {
          mainIPC
            .getMainWindowWebContents()
            .send('update-capture-image', nativeImage.toPNG(), p);
        }
      }
    }

    if (hasHideWindow) {
      mainIPC.show();
    }
    reset();
  });
};

const reset = () => {
  if (captureWins) {
    captureWins.forEach((win) => {
      win.close();
      win = null;
    });
  }
  captureWins = [];
  captureWinIds = [];
  displays = [];
  createCaptureWindows();
};

/**
 * 获取所有屏幕
 * @returns {*}
 */
const getAllDisplays = () => {
  return screen.getAllDisplays();
};

/**
 * 设置globalObject的截图id, 控制政务微信热设置 - 退出 - 重新登录 后能打开截图
 * @returns {*}
 */
const initCaptureWinIds = (displays) => {
  displays.forEach((display) => {
    captureWinIds.push(display.id);
  });
};

const createFolder = () => {
  // 预创建截图保存地址，使用递归创建确保父目录存在
  const result = ensureDir(basepath);
  if (result.code === SUCCESS_CON) {
    state = true;
  } else {
    console.error('创建截图目录失败:', result.errMsg);
    state = false;
  }
};

export { preCapture, hideWins, startScreenshot };
