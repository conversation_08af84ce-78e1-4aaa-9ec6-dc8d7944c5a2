/* 登录表单样式 */

.login-form-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
}

.login-form {
  width: 100%;
  max-width: 400px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  padding: 40px 32px;
  animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 表单头部 */
.form-header {
  text-align: center;
  margin-bottom: 32px;
}

.form-title {
  font-size: 28px;
  font-weight: 700;
  color: #2d3748;
  margin: 0 0 8px 0;
  letter-spacing: -0.5px;
}

.form-subtitle {
  font-size: 14px;
  color: #718096;
  margin: 0;
  font-weight: 400;
}

/* 表单主体 */
.form-body {
  margin-bottom: 32px;
}

.form-group {
  margin-bottom: 24px;
}

.form-group:last-child {
  margin-bottom: 0;
}

.form-label {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: #4a5568;
  margin-bottom: 8px;
  letter-spacing: 0.025em;
}

/* 输入框容器 */
.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.form-input {
  width: 100%;
  height: 48px;
  padding: 0 48px 0 16px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 16px;
  color: #2d3748;
  background-color: #ffffff;
  transition: all 0.2s ease-in-out;
  outline: none;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-input:disabled {
  background-color: #f7fafc;
  color: #a0aec0;
  cursor: not-allowed;
}

.form-input.error {
  border-color: #e53e3e;
  box-shadow: 0 0 0 3px rgba(229, 62, 62, 0.1);
}

.form-input::placeholder {
  color: #a0aec0;
  font-size: 14px;
}

/* 输入框图标 */
.input-icon {
  position: absolute;
  right: 16px;
  font-size: 18px;
  color: #a0aec0;
  pointer-events: none;
  transition: color 0.2s ease-in-out;
}

.password-toggle {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  pointer-events: auto;
  transition: background-color 0.2s ease-in-out;
}

.password-toggle:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.password-toggle:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

/* 错误信息 */
.error-message {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
  color: #e53e3e;
  margin-top: 8px;
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-4px); }
  75% { transform: translateX(4px); }
}

.global-error {
  background-color: #fed7d7;
  border: 1px solid #feb2b2;
  border-radius: 8px;
  padding: 12px 16px;
  margin-bottom: 24px;
  color: #c53030;
}

.field-error {
  margin-top: 6px;
}

.error-icon {
  font-size: 16px;
  flex-shrink: 0;
}

.error-text {
  flex: 1;
  line-height: 1.4;
}

/* 复选框组 */
.checkbox-group {
  margin-bottom: 16px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 14px;
  color: #4a5568;
  user-select: none;
}

.checkbox-input {
  position: absolute;
  opacity: 0;
  pointer-events: none;
}

.checkbox-custom {
  width: 18px;
  height: 18px;
  border: 2px solid #e2e8f0;
  border-radius: 4px;
  margin-right: 12px;
  position: relative;
  transition: all 0.2s ease-in-out;
  flex-shrink: 0;
}

.checkbox-input:checked + .checkbox-custom {
  background-color: #667eea;
  border-color: #667eea;
}

.checkbox-input:checked + .checkbox-custom::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.checkbox-input:focus + .checkbox-custom {
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.checkbox-text {
  line-height: 1.4;
}

/* 表单底部 */
.form-footer {
  text-align: center;
}

/* 登录按钮 */
.login-button {
  width: 100%;
  height: 48px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 16px;
  outline: none;
  letter-spacing: 0.025em;
}

.login-button:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.login-button:active:not(:disabled) {
  transform: translateY(0);
}

.login-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.login-button.loading {
  pointer-events: none;
}

/* 加载动画 */
.loading-spinner {
  width: 18px;
  height: 18px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.button-text {
  line-height: 1;
}

/* 帮助链接 */
.help-links {
  display: flex;
  justify-content: center;
  gap: 16px;
}

.help-link {
  background: none;
  border: none;
  color: #667eea;
  font-size: 14px;
  cursor: pointer;
  text-decoration: none;
  transition: color 0.2s ease-in-out;
  padding: 4px 8px;
  border-radius: 4px;
}

.help-link:hover:not(:disabled) {
  color: #5a67d8;
  text-decoration: underline;
}

.help-link:disabled {
  color: #a0aec0;
  cursor: not-allowed;
}

/* 版本信息 */
.version-info {
  margin-top: 32px;
  text-align: center;
}

.version-text {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 400;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .login-form-container {
    padding: 16px;
  }
  
  .login-form {
    padding: 32px 24px;
    border-radius: 12px;
  }
  
  .form-title {
    font-size: 24px;
  }
  
  .form-input {
    height: 44px;
    font-size: 16px; /* 防止 iOS Safari 缩放 */
  }
  
  .login-button {
    height: 44px;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .login-form {
    background: rgba(26, 32, 44, 0.95);
    color: #e2e8f0;
  }
  
  .form-title {
    color: #f7fafc;
  }
  
  .form-subtitle {
    color: #a0aec0;
  }
  
  .form-label {
    color: #e2e8f0;
  }
  
  .form-input {
    background-color: #2d3748;
    border-color: #4a5568;
    color: #f7fafc;
  }
  
  .form-input:focus {
    border-color: #667eea;
  }
  
  .checkbox-text {
    color: #e2e8f0;
  }
  
  .version-text {
    color: rgba(255, 255, 255, 0.6);
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .form-input {
    border-width: 3px;
  }
  
  .login-button {
    border: 2px solid transparent;
  }
  
  .login-button:focus {
    border-color: #ffffff;
  }
}
