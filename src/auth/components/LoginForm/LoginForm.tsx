import React, { useState, useCallback, useEffect } from 'react';
import { ILoginFormProps, IUserCredentials } from '../../types/auth';
import { VALIDATION_RULES } from '../../utils/constants';
import './LoginForm.module.css';

/**
 * 登录表单组件
 */
export const LoginForm: React.FC<ILoginFormProps> = ({
  onSubmit,
  loading = false,
  error = null,
  rememberMeEnabled = true
}) => {
  const [formData, setFormData] = useState<IUserCredentials>({
    username: '',
    password: '',
    rememberMe: false
  });
  
  const [validationErrors, setValidationErrors] = useState<{
    username?: string;
    password?: string;
  }>({});
  
  const [showPassword, setShowPassword] = useState(false);
  const [isFormValid, setIsFormValid] = useState(false);

  // 验证表单
  const validateForm = useCallback(() => {
    const errors: { username?: string; password?: string } = {};
    
    // 验证用户名
    if (!formData.username) {
      errors.username = '请输入用户名';
    } else if (formData.username.length < VALIDATION_RULES.USERNAME.MIN_LENGTH) {
      errors.username = `用户名至少需要${VALIDATION_RULES.USERNAME.MIN_LENGTH}个字符`;
    } else if (formData.username.length > VALIDATION_RULES.USERNAME.MAX_LENGTH) {
      errors.username = `用户名不能超过${VALIDATION_RULES.USERNAME.MAX_LENGTH}个字符`;
    } else if (!VALIDATION_RULES.USERNAME.PATTERN.test(formData.username)) {
      errors.username = '用户名只能包含字母、数字、下划线和连字符';
    }
    
    // 验证密码
    if (!formData.password) {
      errors.password = '请输入密码';
    } else if (formData.password.length < VALIDATION_RULES.PASSWORD.MIN_LENGTH) {
      errors.password = `密码至少需要${VALIDATION_RULES.PASSWORD.MIN_LENGTH}个字符`;
    } else if (formData.password.length > VALIDATION_RULES.PASSWORD.MAX_LENGTH) {
      errors.password = `密码不能超过${VALIDATION_RULES.PASSWORD.MAX_LENGTH}个字符`;
    }
    
    setValidationErrors(errors);
    const isValid = Object.keys(errors).length === 0;
    setIsFormValid(isValid);
    
    return isValid;
  }, [formData]);

  // 监听表单数据变化，实时验证
  useEffect(() => {
    validateForm();
  }, [validateForm]);

  // 处理输入变化
  const handleInputChange = useCallback((field: keyof IUserCredentials, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  }, []);

  // 处理表单提交
  const handleSubmit = useCallback((e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    if (loading) {
      return;
    }
    
    onSubmit(formData);
  }, [formData, loading, onSubmit, validateForm]);

  // 处理键盘事件
  const handleKeyPress = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && isFormValid && !loading) {
      handleSubmit(e as any);
    }
  }, [handleSubmit, isFormValid, loading]);

  // 切换密码显示状态
  const togglePasswordVisibility = useCallback(() => {
    setShowPassword(prev => !prev);
  }, []);

  return (
    <div className="login-form-container">
      <form className="login-form" onSubmit={handleSubmit} noValidate>
        <div className="form-header">
          <h2 className="form-title">用户登录</h2>
          <p className="form-subtitle">请输入您的账户信息</p>
        </div>

        {/* 全局错误信息 */}
        {error && (
          <div className="error-message global-error" role="alert">
            <span className="error-icon">⚠️</span>
            <span className="error-text">{error}</span>
          </div>
        )}

        <div className="form-body">
          {/* 用户名输入 */}
          <div className="form-group">
            <label htmlFor="username" className="form-label">
              用户名
            </label>
            <div className="input-wrapper">
              <input
                id="username"
                type="text"
                className={`form-input ${validationErrors.username ? 'error' : ''}`}
                placeholder="请输入用户名"
                value={formData.username}
                onChange={(e) => handleInputChange('username', e.target.value)}
                onKeyPress={handleKeyPress}
                disabled={loading}
                autoComplete="username"
                aria-describedby={validationErrors.username ? 'username-error' : undefined}
                aria-invalid={!!validationErrors.username}
              />
              <span className="input-icon user-icon">👤</span>
            </div>
            {validationErrors.username && (
              <div id="username-error" className="error-message field-error" role="alert">
                <span className="error-text">{validationErrors.username}</span>
              </div>
            )}
          </div>

          {/* 密码输入 */}
          <div className="form-group">
            <label htmlFor="password" className="form-label">
              密码
            </label>
            <div className="input-wrapper">
              <input
                id="password"
                type={showPassword ? 'text' : 'password'}
                className={`form-input ${validationErrors.password ? 'error' : ''}`}
                placeholder="请输入密码"
                value={formData.password}
                onChange={(e) => handleInputChange('password', e.target.value)}
                onKeyPress={handleKeyPress}
                disabled={loading}
                autoComplete="current-password"
                aria-describedby={validationErrors.password ? 'password-error' : undefined}
                aria-invalid={!!validationErrors.password}
              />
              <button
                type="button"
                className="input-icon password-toggle"
                onClick={togglePasswordVisibility}
                disabled={loading}
                aria-label={showPassword ? '隐藏密码' : '显示密码'}
              >
                {showPassword ? '🙈' : '👁️'}
              </button>
            </div>
            {validationErrors.password && (
              <div id="password-error" className="error-message field-error" role="alert">
                <span className="error-text">{validationErrors.password}</span>
              </div>
            )}
          </div>

          {/* 记住我选项 */}
          {rememberMeEnabled && (
            <div className="form-group checkbox-group">
              <label className="checkbox-label">
                <input
                  type="checkbox"
                  className="checkbox-input"
                  checked={formData.rememberMe}
                  onChange={(e) => handleInputChange('rememberMe', e.target.checked)}
                  disabled={loading}
                />
                <span className="checkbox-custom"></span>
                <span className="checkbox-text">记住我</span>
              </label>
            </div>
          )}
        </div>

        <div className="form-footer">
          {/* 登录按钮 */}
          <button
            type="submit"
            className={`login-button ${loading ? 'loading' : ''} ${!isFormValid ? 'disabled' : ''}`}
            disabled={!isFormValid || loading}
            aria-label={loading ? '正在登录...' : '登录'}
          >
            {loading ? (
              <>
                <span className="loading-spinner"></span>
                <span className="button-text">正在登录...</span>
              </>
            ) : (
              <span className="button-text">登录</span>
            )}
          </button>

          {/* 帮助链接 */}
          <div className="help-links">
            <button
              type="button"
              className="help-link"
              onClick={() => {
                // TODO: 实现忘记密码功能
                console.log('Forgot password clicked');
              }}
              disabled={loading}
            >
              忘记密码？
            </button>
          </div>
        </div>
      </form>

      {/* 版本信息 */}
      <div className="version-info">
        <span className="version-text">LinkFlow v{process.env.npm_package_version || '1.0.0'}</span>
      </div>
    </div>
  );
};

export default LoginForm;
