/**
 * 认证系统类型定义
 */

// 认证步骤枚举
export enum AuthStep {
  LOGIN = 'login',
  GEETEST = 'geetest',
  PHONE_VERIFY = 'phone_verify',
  SUCCESS = 'success'
}

// 风险等级
export type RiskLevel = 'LOW' | 'MEDIUM' | 'HIGH';

// 用户凭据接口
export interface IUserCredentials {
  username: string;
  password: string;
  rememberMe?: boolean;
}

// 极验验证数据接口
export interface IGeetestData {
  geetest_challenge: string;
  geetest_validate: string;
  geetest_seccode: string;
}

// 手机验证数据接口
export interface IPhoneVerifyData {
  phoneNumber: string;
  verificationCode: string;
  requestId?: string;
}

// 认证结果接口
export interface IAuthResult {
  success: boolean;
  error?: string;
  nextStep?: AuthStep;
  data?: any;
}

// 用户信息接口
export interface IUserInfo {
  id: string;
  username: string;
  displayName?: string;
  email?: string;
  phoneNumber?: string;
  avatar?: string;
  roles?: string[];
  permissions?: string[];
}

// 认证状态接口
export interface IAuthStatus {
  isAuthenticated: boolean;
  currentStep: AuthStep;
  user: IUserInfo | null;
  lastLoginTime: Date | null;
  deviceId: string | null;
}

// 认证数据接口
export interface IAuthData {
  credentials: IUserCredentials;
  geetestData?: IGeetestData;
  phoneData?: IPhoneVerifyData;
  loginTime: Date;
  geetestVerified?: boolean;
  phoneVerified?: boolean;
}

// Token 数据接口
export interface ITokenData {
  userId: string;
  username: string;
  issuedAt: number;
  expiresAt: number;
  deviceId: string;
  sessionId: string;
  permissions: string[];
}

// 登录尝试记录接口
export interface ILoginAttempt {
  username: string;
  timestamp: number;
  success: boolean;
  deviceId: string;
  ipAddress: string;
  userAgent: string;
  failureReason?: string;
}

// 安全检查结果接口
export interface ISecurityCheck {
  isValid: boolean;
  reason?: string;
  riskLevel: RiskLevel;
  deviceId?: string;
}

// 登录结果接口
export interface ILoginResult {
  success: boolean;
  error?: string;
  nextStep?: AuthStep;
  requiresGeetest?: boolean;
  requiresPhoneVerify?: boolean;
  user?: IUserInfo;
}

// 极验验证结果接口
export interface IGeetestResult {
  success: boolean;
  error?: string;
  geetest_challenge?: string;
  geetest_validate?: string;
  geetest_seccode?: string;
}

// 短信发送结果接口
export interface ISmsResult {
  success: boolean;
  error?: string;
  requestId?: string;
  cooldownTime?: number;
}

// 验证结果接口
export interface IVerifyResult {
  success: boolean;
  error?: string;
  nextStep?: AuthStep;
}

// 极验配置接口
export interface IGeetestConfig {
  gt: string;
  challenge: string;
  offline?: boolean;
  new_captcha?: boolean;
  product?: 'popup' | 'float' | 'embed' | 'bind';
  width?: string;
  lang?: 'zh-cn' | 'en' | 'zh-tw' | 'ja' | 'ko';
  https?: boolean;
  timeout?: number;
  remoteConfig?: boolean;
}

// 认证管理器接口
export interface IAuthManager {
  initialize(): Promise<void>;
  validateCredentials(credentials: IUserCredentials): Promise<IAuthResult>;
  handleGeetestVerification(geetestData: IGeetestData): Promise<boolean>;
  handlePhoneVerification(phoneData: IPhoneVerifyData): Promise<boolean>;
  getAuthStatus(): IAuthStatus;
  clearAuth(): Promise<void>;
}

// Token 管理器接口
export interface ITokenManager {
  initialize(): Promise<void>;
  generateToken(authData: Partial<IAuthData>): Promise<string>;
  saveToken(token: string): Promise<void>;
  getValidToken(): Promise<string | null>;
  validateToken(token: string): Promise<boolean>;
  refreshToken(token: string): Promise<string | null>;
  clearTokens(): Promise<void>;
}

// 安全管理器接口
export interface ISecurityManager {
  initialize(): Promise<void>;
  validateLoginAttempt(credentials: IUserCredentials): Promise<ISecurityCheck>;
  recordSuccessfulLogin(username: string): Promise<void>;
  recordFailedLogin(username: string, reason?: string): Promise<void>;
  getDeviceId(): Promise<string>;
  isDeviceTrusted(): Promise<boolean>;
  addTrustedDevice(deviceId?: string): Promise<void>;
}

// 认证服务接口
export interface IAuthService {
  login(credentials: IUserCredentials): Promise<ILoginResult>;
  geetestVerify(challenge: string): Promise<IGeetestResult>;
  sendSmsCode(phoneNumber: string): Promise<ISmsResult>;
  verifyPhone(phoneNumber: string, code: string): Promise<IVerifyResult>;
  getUserInfo(): Promise<IUserInfo>;
  logout(): Promise<void>;
}

// 极验服务接口
export interface IGeetestService {
  initialize(config: IGeetestConfig): Promise<void>;
  showVerification(): Promise<IGeetestResult>;
  reset(): void;
  destroy(): void;
}

// 短信服务接口
export interface ISmsService {
  sendVerificationCode(phoneNumber: string): Promise<ISmsResult>;
  verifyCode(phoneNumber: string, code: string): Promise<IVerifyResult>;
  getCodeCooldown(phoneNumber: string): number;
}

// 认证状态管理接口
export interface IAuthStore {
  state: IAuthState;
  login(credentials: IUserCredentials): Promise<ILoginResult>;
  geetestVerify(geetestData: IGeetestData): Promise<boolean>;
  phoneVerify(phoneData: IPhoneVerifyData): Promise<boolean>;
  logout(): Promise<void>;
  clearError(): void;
  setLoading(loading: boolean): void;
}

// 认证状态接口
export interface IAuthState {
  // 认证状态
  isAuthenticated: boolean;
  isLoading: boolean;
  currentStep: AuthStep;
  
  // 用户信息
  user: IUserInfo | null;
  token: string | null;
  
  // 验证状态
  geetestPassed: boolean;
  phoneVerified: boolean;
  
  // 错误信息
  error: string | null;
  
  // 配置信息
  geetestConfig: IGeetestConfig | null;
  phoneNumber: string | null;
  
  // 安全信息
  deviceId: string | null;
  riskLevel: RiskLevel;
  
  // 时间信息
  loginTime: Date | null;
  lastActivity: Date | null;
}

// 认证事件类型
export type AuthEventType = 
  | 'LOGIN_START'
  | 'LOGIN_SUCCESS'
  | 'LOGIN_FAILED'
  | 'GEETEST_START'
  | 'GEETEST_SUCCESS'
  | 'GEETEST_FAILED'
  | 'PHONE_VERIFY_START'
  | 'PHONE_VERIFY_SUCCESS'
  | 'PHONE_VERIFY_FAILED'
  | 'AUTH_COMPLETE'
  | 'LOGOUT'
  | 'TOKEN_REFRESH'
  | 'SECURITY_ALERT';

// 认证事件接口
export interface IAuthEvent {
  type: AuthEventType;
  timestamp: number;
  data?: any;
  error?: string;
}

// 认证配置接口
export interface IAuthConfig {
  // 极验配置
  geetestEnabled: boolean;
  geetestGt?: string;
  
  // 短信验证配置
  smsEnabled: boolean;
  smsProvider?: string;
  
  // 安全配置
  maxLoginAttempts: number;
  lockoutDuration: number;
  tokenExpiration: number;
  
  // 设备信任配置
  deviceTrustEnabled: boolean;
  trustDeviceDuration: number;
  
  // 其他配置
  rememberMeEnabled: boolean;
  autoLogoutEnabled: boolean;
  autoLogoutDuration: number;
}

// 认证错误类型
export enum AuthErrorType {
  INVALID_CREDENTIALS = 'INVALID_CREDENTIALS',
  ACCOUNT_LOCKED = 'ACCOUNT_LOCKED',
  GEETEST_FAILED = 'GEETEST_FAILED',
  PHONE_VERIFY_FAILED = 'PHONE_VERIFY_FAILED',
  TOKEN_EXPIRED = 'TOKEN_EXPIRED',
  DEVICE_NOT_TRUSTED = 'DEVICE_NOT_TRUSTED',
  NETWORK_ERROR = 'NETWORK_ERROR',
  SECURITY_VIOLATION = 'SECURITY_VIOLATION',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

// 认证错误接口
export interface IAuthError {
  type: AuthErrorType;
  message: string;
  details?: any;
  timestamp: number;
}

// 组件属性接口
export interface ILoginFormProps {
  onSubmit: (credentials: IUserCredentials) => void;
  loading?: boolean;
  error?: string;
  rememberMeEnabled?: boolean;
}

export interface IGeetestVerifyProps {
  config: IGeetestConfig;
  onSuccess: (result: IGeetestResult) => void;
  onError: (error: string) => void;
  onReady?: () => void;
}

export interface IPhoneVerifyProps {
  phoneNumber: string;
  onVerify: (code: string) => void;
  onSendCode: () => void;
  loading?: boolean;
  error?: string;
  cooldownTime?: number;
}

export interface IAuthLayoutProps {
  children: React.ReactNode;
  title?: string;
  subtitle?: string;
  showLogo?: boolean;
  showProgress?: boolean;
  currentStep?: AuthStep;
  totalSteps?: number;
}
