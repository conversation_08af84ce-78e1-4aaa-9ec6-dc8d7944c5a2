import { rendererIPC } from '@htElectronSDK/renderer/ipc';
import {
  IAuthService,
  IUserCredentials,
  ILoginResult,
  IGeetestResult,
  ISmsResult,
  IVerifyResult,
  IUserInfo,
  AuthStep
} from '../types/auth';
import {
  AUTH_LOGIN_REQUEST,
  AUTH_LOGIN_RESPONSE,
  AUTH_GEETEST_REQUEST,
  AUTH_GEETEST_RESPONSE,
  AUTH_PHONE_VERIFY_REQUEST,
  AUTH_PHONE_VERIFY_RESPONSE,
  AUTH_STATUS_REQUEST,
  AUTH_STATUS_RESPONSE,
  AUTH_LOGOUT_REQUEST,
  AUTH_LOGOUT_RESPONSE,
  SMS_SEND_CODE_REQUEST,
  SMS_SEND_CODE_RESPONSE,
  SMS_VERIFY_CODE_REQUEST,
  SMS_VERIFY_CODE_RESPONSE,
  TOKEN_GET_REQUEST,
  TOKEN_GET_RESPONSE
} from '../utils/constants';

/**
 * 渲染进程认证服务
 * 负责与主进程通信，处理认证相关的业务逻辑
 */
export class AuthService implements IAuthService {
  private static instance: AuthService;
  private currentUser: IUserInfo | null = null;
  private authToken: string | null = null;

  private constructor() {
    this.initializeEventListeners();
  }

  /**
   * 获取单例实例
   */
  static getInstance(): AuthService {
    if (!AuthService.instance) {
      AuthService.instance = new AuthService();
    }
    return AuthService.instance;
  }

  /**
   * 用户登录
   */
  async login(credentials: IUserCredentials): Promise<ILoginResult> {
    try {
      console.log('AuthService: Starting login process', { username: credentials.username });
      
      // 发送登录请求到主进程
      const response = await this.sendIpcRequest(AUTH_LOGIN_REQUEST, credentials);
      
      if (response.success) {
        console.log('AuthService: Login successful, next step:', response.nextStep);
        
        return {
          success: true,
          nextStep: response.nextStep,
          requiresGeetest: response.nextStep === AuthStep.GEETEST,
          requiresPhoneVerify: response.nextStep === AuthStep.PHONE_VERIFY,
          user: response.data
        };
      } else {
        console.warn('AuthService: Login failed:', response.error);
        
        return {
          success: false,
          error: response.error,
          nextStep: AuthStep.LOGIN
        };
      }
    } catch (error) {
      console.error('AuthService: Login error:', error);
      return {
        success: false,
        error: '登录服务异常，请稍后重试',
        nextStep: AuthStep.LOGIN
      };
    }
  }

  /**
   * 极验验证
   */
  async geetestVerify(challenge: string): Promise<IGeetestResult> {
    try {
      console.log('AuthService: Starting Geetest verification');
      
      // 这里应该集成极验 SDK 的验证逻辑
      // 暂时返回模拟结果
      const geetestData = {
        geetest_challenge: challenge,
        geetest_validate: 'mock_validate',
        geetest_seccode: 'mock_seccode'
      };
      
      // 发送极验验证请求到主进程
      const response = await this.sendIpcRequest(AUTH_GEETEST_REQUEST, geetestData);
      
      if (response) {
        console.log('AuthService: Geetest verification successful');
        return {
          success: true,
          geetest_challenge: geetestData.geetest_challenge,
          geetest_validate: geetestData.geetest_validate,
          geetest_seccode: geetestData.geetest_seccode
        };
      } else {
        console.warn('AuthService: Geetest verification failed');
        return {
          success: false,
          error: '安全验证失败，请重试'
        };
      }
    } catch (error) {
      console.error('AuthService: Geetest verification error:', error);
      return {
        success: false,
        error: '验证服务异常，请稍后重试'
      };
    }
  }

  /**
   * 发送验证码
   */
  async sendSmsCode(phoneNumber: string): Promise<ISmsResult> {
    try {
      console.log('AuthService: Sending SMS code to:', phoneNumber);
      
      // 发送短信验证码请求到主进程
      const response = await this.sendIpcRequest(SMS_SEND_CODE_REQUEST, { phoneNumber });
      
      if (response.success) {
        console.log('AuthService: SMS code sent successfully');
        return {
          success: true,
          requestId: response.requestId,
          cooldownTime: response.cooldownTime || 60
        };
      } else {
        console.warn('AuthService: Failed to send SMS code:', response.error);
        return {
          success: false,
          error: response.error || '发送验证码失败'
        };
      }
    } catch (error) {
      console.error('AuthService: SMS send error:', error);
      return {
        success: false,
        error: '短信服务异常，请稍后重试'
      };
    }
  }

  /**
   * 验证手机号
   */
  async verifyPhone(phoneNumber: string, code: string): Promise<IVerifyResult> {
    try {
      console.log('AuthService: Verifying phone number:', phoneNumber);
      
      const phoneData = {
        phoneNumber,
        verificationCode: code
      };
      
      // 发送手机验证请求到主进程
      const response = await this.sendIpcRequest(AUTH_PHONE_VERIFY_REQUEST, phoneData);
      
      if (response) {
        console.log('AuthService: Phone verification successful');
        return {
          success: true,
          nextStep: AuthStep.SUCCESS
        };
      } else {
        console.warn('AuthService: Phone verification failed');
        return {
          success: false,
          error: '手机验证失败，请检查验证码',
          nextStep: AuthStep.PHONE_VERIFY
        };
      }
    } catch (error) {
      console.error('AuthService: Phone verification error:', error);
      return {
        success: false,
        error: '验证服务异常，请稍后重试',
        nextStep: AuthStep.PHONE_VERIFY
      };
    }
  }

  /**
   * 获取用户信息
   */
  async getUserInfo(): Promise<IUserInfo> {
    try {
      if (this.currentUser) {
        return this.currentUser;
      }
      
      // 从主进程获取用户信息
      const response = await this.sendIpcRequest(AUTH_STATUS_REQUEST, {});
      
      if (response.user) {
        this.currentUser = response.user;
        return response.user;
      } else {
        throw new Error('No user information available');
      }
    } catch (error) {
      console.error('AuthService: Error getting user info:', error);
      throw error;
    }
  }

  /**
   * 用户登出
   */
  async logout(): Promise<void> {
    try {
      console.log('AuthService: Starting logout process');
      
      // 发送登出请求到主进程
      await this.sendIpcRequest(AUTH_LOGOUT_REQUEST, {});
      
      // 清除本地状态
      this.currentUser = null;
      this.authToken = null;
      
      console.log('AuthService: Logout completed');
    } catch (error) {
      console.error('AuthService: Logout error:', error);
      throw error;
    }
  }

  /**
   * 检查认证状态
   */
  async checkAuthStatus(): Promise<boolean> {
    try {
      const response = await this.sendIpcRequest(AUTH_STATUS_REQUEST, {});
      return response.isAuthenticated || false;
    } catch (error) {
      console.error('AuthService: Error checking auth status:', error);
      return false;
    }
  }

  /**
   * 获取当前 Token
   */
  async getCurrentToken(): Promise<string | null> {
    try {
      if (this.authToken) {
        return this.authToken;
      }
      
      const response = await this.sendIpcRequest(TOKEN_GET_REQUEST, {});
      if (response.token) {
        this.authToken = response.token;
        return response.token;
      }
      
      return null;
    } catch (error) {
      console.error('AuthService: Error getting current token:', error);
      return null;
    }
  }

  /**
   * 刷新 Token
   */
  async refreshToken(): Promise<string | null> {
    try {
      const currentToken = await this.getCurrentToken();
      if (!currentToken) {
        return null;
      }
      
      const response = await this.sendIpcRequest('token.refresh.request', { token: currentToken });
      if (response.token) {
        this.authToken = response.token;
        return response.token;
      }
      
      return null;
    } catch (error) {
      console.error('AuthService: Error refreshing token:', error);
      return null;
    }
  }

  /**
   * 设置认证状态监听器
   */
  onAuthStateChange(callback: (isAuthenticated: boolean, user?: IUserInfo) => void): void {
    // 监听认证状态变化
    rendererIPC.addListener('auth.state.changed', (data) => {
      this.currentUser = data.user || null;
      this.authToken = data.token || null;
      callback(data.isAuthenticated, data.user);
    });
  }

  /**
   * 初始化事件监听器
   */
  private initializeEventListeners(): void {
    // 监听登录响应
    rendererIPC.addListener(AUTH_LOGIN_RESPONSE, (response) => {
      console.log('AuthService: Received login response:', response);
    });

    // 监听极验验证响应
    rendererIPC.addListener(AUTH_GEETEST_RESPONSE, (response) => {
      console.log('AuthService: Received Geetest response:', response);
    });

    // 监听手机验证响应
    rendererIPC.addListener(AUTH_PHONE_VERIFY_RESPONSE, (response) => {
      console.log('AuthService: Received phone verify response:', response);
    });

    // 监听短信发送响应
    rendererIPC.addListener(SMS_SEND_CODE_RESPONSE, (response) => {
      console.log('AuthService: Received SMS send response:', response);
    });

    // 监听认证状态响应
    rendererIPC.addListener(AUTH_STATUS_RESPONSE, (response) => {
      console.log('AuthService: Received auth status response:', response);
      if (response.user) {
        this.currentUser = response.user;
      }
    });

    // 监听登出响应
    rendererIPC.addListener(AUTH_LOGOUT_RESPONSE, (response) => {
      console.log('AuthService: Received logout response:', response);
      this.currentUser = null;
      this.authToken = null;
    });

    // 监听 Token 响应
    rendererIPC.addListener(TOKEN_GET_RESPONSE, (response) => {
      console.log('AuthService: Received token response:', response);
      if (response.token) {
        this.authToken = response.token;
      }
    });
  }

  /**
   * 发送 IPC 请求并等待响应
   */
  private async sendIpcRequest(requestId: string, data: any): Promise<any> {
    return new Promise((resolve, reject) => {
      const responseId = requestId.replace('.request', '.response');
      const timeout = setTimeout(() => {
        reject(new Error('IPC request timeout'));
      }, 10000); // 10秒超时

      // 监听响应
      const handleResponse = (response: any) => {
        clearTimeout(timeout);
        rendererIPC.removeListener(responseId, handleResponse);
        resolve(response);
      };

      rendererIPC.addListener(responseId, handleResponse);
      
      // 发送请求
      rendererIPC.send({
        id: requestId,
        params: data
      });
    });
  }
}

// 导出单例实例
export const authService = AuthService.getInstance();
