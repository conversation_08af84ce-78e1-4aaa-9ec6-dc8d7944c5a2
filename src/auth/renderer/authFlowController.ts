import { 
  AuthStep, 
  IUserCredentials, 
  IGeetestData, 
  IPhoneVerifyData, 
  IAuthData,
  AuthEventType,
  IAuthEvent,
  IAuthError,
  AuthErrorType
} from '../types/auth';
import { authService } from './authService';

/**
 * 认证流程控制器
 * 负责管理整个认证流程的状态和步骤转换
 */
export class AuthFlowController {
  private currentStep: AuthStep = AuthStep.LOGIN;
  private authData: Partial<IAuthData> = {};
  private eventListeners: Map<AuthEventType, ((event: IAuthEvent) => void)[]> = new Map();
  private isProcessing = false;

  constructor() {
    this.initializeEventListeners();
  }

  /**
   * 获取当前步骤
   */
  getCurrentStep(): AuthStep {
    return this.currentStep;
  }

  /**
   * 获取认证数据
   */
  getAuthData(): Partial<IAuthData> {
    return { ...this.authData };
  }

  /**
   * 检查是否正在处理
   */
  isProcessingAuth(): boolean {
    return this.isProcessing;
  }

  /**
   * 开始认证流程
   */
  async startAuthFlow(): Promise<void> {
    try {
      console.log('AuthFlowController: Starting authentication flow');
      
      this.currentStep = AuthStep.LOGIN;
      this.authData = {};
      this.isProcessing = false;
      
      this.emitEvent('LOGIN_START', { step: this.currentStep });
    } catch (error) {
      console.error('AuthFlowController: Error starting auth flow:', error);
      await this.handleAuthError(error);
    }
  }

  /**
   * 处理登录步骤
   */
  async handleLogin(credentials: IUserCredentials): Promise<void> {
    if (this.isProcessing) {
      console.warn('AuthFlowController: Authentication already in progress');
      return;
    }

    try {
      this.isProcessing = true;
      console.log('AuthFlowController: Processing login step');
      
      this.emitEvent('LOGIN_START', { credentials: { username: credentials.username } });
      
      // 保存凭据
      this.authData.credentials = credentials;
      this.authData.loginTime = new Date();
      
      // 调用认证服务
      const result = await authService.login(credentials);
      
      if (result.success) {
        console.log('AuthFlowController: Login successful, next step:', result.nextStep);
        
        this.emitEvent('LOGIN_SUCCESS', { 
          nextStep: result.nextStep,
          user: result.user 
        });
        
        // 根据返回的下一步决定流程
        if (result.nextStep) {
          await this.moveToStep(result.nextStep);
        } else {
          await this.completeAuthentication();
        }
      } else {
        console.warn('AuthFlowController: Login failed:', result.error);
        
        this.emitEvent('LOGIN_FAILED', { 
          error: result.error,
          nextStep: result.nextStep 
        });
        
        throw new Error(result.error || '登录失败');
      }
    } catch (error) {
      await this.handleAuthError(error, AuthErrorType.INVALID_CREDENTIALS);
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * 处理极验验证步骤
   */
  async handleGeetestVerify(geetestData: IGeetestData): Promise<void> {
    if (this.isProcessing) {
      console.warn('AuthFlowController: Authentication already in progress');
      return;
    }

    try {
      this.isProcessing = true;
      console.log('AuthFlowController: Processing Geetest verification step');
      
      this.emitEvent('GEETEST_START', { geetestData });
      
      // 保存极验数据
      this.authData.geetestData = geetestData;
      
      // 调用认证服务
      const result = await authService.geetestVerify(geetestData.geetest_challenge);
      
      if (result.success) {
        console.log('AuthFlowController: Geetest verification successful');
        
        this.authData.geetestVerified = true;
        
        this.emitEvent('GEETEST_SUCCESS', { geetestData });
        
        // 移动到下一步（通常是手机验证）
        await this.moveToStep(AuthStep.PHONE_VERIFY);
      } else {
        console.warn('AuthFlowController: Geetest verification failed:', result.error);
        
        this.emitEvent('GEETEST_FAILED', { 
          error: result.error 
        });
        
        throw new Error(result.error || '安全验证失败');
      }
    } catch (error) {
      await this.handleAuthError(error, AuthErrorType.GEETEST_FAILED);
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * 处理手机验证步骤
   */
  async handlePhoneVerify(phoneData: IPhoneVerifyData): Promise<void> {
    if (this.isProcessing) {
      console.warn('AuthFlowController: Authentication already in progress');
      return;
    }

    try {
      this.isProcessing = true;
      console.log('AuthFlowController: Processing phone verification step');
      
      this.emitEvent('PHONE_VERIFY_START', { phoneData });
      
      // 保存手机验证数据
      this.authData.phoneData = phoneData;
      
      // 调用认证服务
      const result = await authService.verifyPhone(phoneData.phoneNumber, phoneData.verificationCode);
      
      if (result.success) {
        console.log('AuthFlowController: Phone verification successful');
        
        this.authData.phoneVerified = true;
        
        this.emitEvent('PHONE_VERIFY_SUCCESS', { phoneData });
        
        // 完成认证流程
        await this.completeAuthentication();
      } else {
        console.warn('AuthFlowController: Phone verification failed:', result.error);
        
        this.emitEvent('PHONE_VERIFY_FAILED', { 
          error: result.error 
        });
        
        throw new Error(result.error || '手机验证失败');
      }
    } catch (error) {
      await this.handleAuthError(error, AuthErrorType.PHONE_VERIFY_FAILED);
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * 发送短信验证码
   */
  async sendSmsCode(phoneNumber: string): Promise<boolean> {
    try {
      console.log('AuthFlowController: Sending SMS code to:', phoneNumber);
      
      const result = await authService.sendSmsCode(phoneNumber);
      
      if (result.success) {
        console.log('AuthFlowController: SMS code sent successfully');
        return true;
      } else {
        console.warn('AuthFlowController: Failed to send SMS code:', result.error);
        throw new Error(result.error || '发送验证码失败');
      }
    } catch (error) {
      console.error('AuthFlowController: Error sending SMS code:', error);
      throw error;
    }
  }

  /**
   * 重置认证流程
   */
  async resetAuthFlow(): Promise<void> {
    try {
      console.log('AuthFlowController: Resetting authentication flow');
      
      this.isProcessing = false;
      this.currentStep = AuthStep.LOGIN;
      this.authData = {};
      
      // 清除认证状态
      await authService.logout();
      
      this.emitEvent('LOGOUT', {});
    } catch (error) {
      console.error('AuthFlowController: Error resetting auth flow:', error);
    }
  }

  /**
   * 添加事件监听器
   */
  addEventListener(eventType: AuthEventType, listener: (event: IAuthEvent) => void): void {
    if (!this.eventListeners.has(eventType)) {
      this.eventListeners.set(eventType, []);
    }
    this.eventListeners.get(eventType)!.push(listener);
  }

  /**
   * 移除事件监听器
   */
  removeEventListener(eventType: AuthEventType, listener: (event: IAuthEvent) => void): void {
    const listeners = this.eventListeners.get(eventType);
    if (listeners) {
      const index = listeners.indexOf(listener);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  /**
   * 移动到指定步骤
   */
  private async moveToStep(step: AuthStep): Promise<void> {
    console.log(`AuthFlowController: Moving from ${this.currentStep} to ${step}`);
    
    this.currentStep = step;
    
    // 根据步骤执行相应的初始化逻辑
    switch (step) {
      case AuthStep.GEETEST:
        // 初始化极验验证
        await this.initializeGeetest();
        break;
      case AuthStep.PHONE_VERIFY:
        // 初始化手机验证
        await this.initializePhoneVerify();
        break;
      case AuthStep.SUCCESS:
        // 认证成功
        await this.completeAuthentication();
        break;
    }
  }

  /**
   * 完成认证流程
   */
  private async completeAuthentication(): Promise<void> {
    try {
      console.log('AuthFlowController: Completing authentication');
      
      this.currentStep = AuthStep.SUCCESS;
      
      // 获取用户信息
      const userInfo = await authService.getUserInfo();
      
      this.emitEvent('AUTH_COMPLETE', { 
        user: userInfo,
        authData: this.authData 
      });
      
      console.log('AuthFlowController: Authentication completed successfully');
    } catch (error) {
      console.error('AuthFlowController: Error completing authentication:', error);
      await this.handleAuthError(error);
    }
  }

  /**
   * 处理认证错误
   */
  private async handleAuthError(error: any, errorType: AuthErrorType = AuthErrorType.UNKNOWN_ERROR): Promise<void> {
    console.error('AuthFlowController: Authentication error:', error);
    
    const authError: IAuthError = {
      type: errorType,
      message: error.message || '认证过程中发生错误',
      details: error,
      timestamp: Date.now()
    };
    
    this.emitEvent('SECURITY_ALERT', { error: authError });
    
    // 根据错误类型决定是否重置流程
    if (this.shouldResetOnError(errorType)) {
      await this.resetAuthFlow();
    }
  }

  /**
   * 判断是否应该在错误时重置流程
   */
  private shouldResetOnError(errorType: AuthErrorType): boolean {
    const resetErrorTypes = [
      AuthErrorType.SECURITY_VIOLATION,
      AuthErrorType.TOKEN_EXPIRED,
      AuthErrorType.DEVICE_NOT_TRUSTED
    ];
    
    return resetErrorTypes.includes(errorType);
  }

  /**
   * 初始化极验验证
   */
  private async initializeGeetest(): Promise<void> {
    try {
      console.log('AuthFlowController: Initializing Geetest verification');
      
      // TODO: 实现极验初始化逻辑
      // 这里应该获取极验配置并初始化极验 SDK
      
    } catch (error) {
      console.error('AuthFlowController: Error initializing Geetest:', error);
      throw error;
    }
  }

  /**
   * 初始化手机验证
   */
  private async initializePhoneVerify(): Promise<void> {
    try {
      console.log('AuthFlowController: Initializing phone verification');
      
      // TODO: 实现手机验证初始化逻辑
      // 这里可以预加载手机号等信息
      
    } catch (error) {
      console.error('AuthFlowController: Error initializing phone verification:', error);
      throw error;
    }
  }

  /**
   * 初始化事件监听器
   */
  private initializeEventListeners(): void {
    // 监听认证服务的状态变化
    authService.onAuthStateChange((isAuthenticated, user) => {
      if (isAuthenticated && user) {
        this.emitEvent('AUTH_COMPLETE', { user });
      } else {
        this.emitEvent('LOGOUT', {});
      }
    });
  }

  /**
   * 发送事件
   */
  private emitEvent(eventType: AuthEventType, data?: any): void {
    const event: IAuthEvent = {
      type: eventType,
      timestamp: Date.now(),
      data
    };
    
    console.log('AuthFlowController: Emitting event:', eventType, data);
    
    const listeners = this.eventListeners.get(eventType);
    if (listeners) {
      listeners.forEach(listener => {
        try {
          listener(event);
        } catch (error) {
          console.error('AuthFlowController: Error in event listener:', error);
        }
      });
    }
  }
}

// 导出单例实例
export const authFlowController = new AuthFlowController();
