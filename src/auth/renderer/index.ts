/**
 * 渲染进程认证模块入口文件
 */

import { authService } from './authService';
import { authFlowController } from './authFlowController';

/**
 * 初始化渲染进程认证系统
 */
export const initRendererAuthSystem = async (): Promise<void> => {
  try {
    console.log('RendererAuthSystem: Initializing renderer authentication system');
    
    // 检查现有认证状态
    const isAuthenticated = await authService.checkAuthStatus();
    
    if (isAuthenticated) {
      console.log('RendererAuthSystem: Found existing authentication');
      // 如果已经认证，可以直接进入主界面
    } else {
      console.log('RendererAuthSystem: No existing authentication found');
      // 启动认证流程
      await authFlowController.startAuthFlow();
    }
    
    console.log('RendererAuthSystem: Renderer authentication system initialized successfully');
  } catch (error) {
    console.error('RendererAuthSystem: Failed to initialize renderer authentication system', error);
    throw error;
  }
};

/**
 * 清理渲染进程认证系统
 */
export const cleanupRendererAuthSystem = async (): Promise<void> => {
  try {
    console.log('RendererAuthSystem: Cleaning up renderer authentication system');
    
    // 重置认证流程
    await authFlowController.resetAuthFlow();
    
    console.log('RendererAuthSystem: Renderer authentication system cleaned up successfully');
  } catch (error) {
    console.error('RendererAuthSystem: Error cleaning up renderer authentication system', error);
  }
};

// 导出服务和控制器实例
export { authService } from './authService';
export { authFlowController } from './authFlowController';
