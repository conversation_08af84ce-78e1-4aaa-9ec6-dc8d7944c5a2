import { ipcMain } from 'electron';
import LOG from '@htElectronSDK/main/log';
import { mainIPC } from '@htElectronSDK/main/ipc';
import { EXCLUSIVE_CON } from '@htElectronSDK/constant/ipc';
import { TokenManager } from './tokenManager';
import { SecurityManager } from './securityManager';
import {
  IAuthManager,
  IUserCredentials,
  IAuthResult,
  IGeetestData,
  IPhoneVerifyData,
  IAuthStatus,
  AuthStep,
  IAuthData
} from '../types/auth';
import {
  AUTH_LOGIN_REQUEST,
  AUTH_LOGIN_RESPONSE,
  AUTH_GEETEST_REQUEST,
  AUTH_GEETEST_RESPONSE,
  AUTH_PHONE_VERIFY_REQUEST,
  AUTH_PHONE_VERIFY_RESPONSE,
  AUTH_STATUS_REQUEST,
  AUTH_STATUS_RESPONSE,
  AUTH_CLEAR_REQUEST,
  AUTH_CLEAR_RESPONSE
} from '../utils/constants';

/**
 * 主进程认证管理器
 * 负责处理认证相关的核心逻辑和安全控制
 */
export class AuthManager implements IAuthManager {
  private tokenManager: TokenManager;
  private securityManager: SecurityManager;
  private currentAuthData: Partial<IAuthData> = {};
  private authStatus: IAuthStatus = {
    isAuthenticated: false,
    currentStep: AuthStep.LOGIN,
    user: null,
    lastLoginTime: null,
    deviceId: null
  };

  constructor() {
    this.tokenManager = new TokenManager();
    this.securityManager = new SecurityManager();
    this.initializeIpcHandlers();
  }

  /**
   * 初始化认证系统
   */
  async initialize(): Promise<void> {
    try {
      LOG.info('AuthManager: Initializing authentication system');
      
      // 初始化 Token 管理器
      await this.tokenManager.initialize();
      
      // 初始化安全管理器
      await this.securityManager.initialize();
      
      // 检查是否有有效的认证状态
      await this.checkExistingAuth();
      
      LOG.info('AuthManager: Authentication system initialized successfully');
    } catch (error) {
      LOG.error('AuthManager: Failed to initialize authentication system', error);
      throw error;
    }
  }

  /**
   * 验证用户凭据
   */
  async validateCredentials(credentials: IUserCredentials): Promise<IAuthResult> {
    try {
      LOG.info('AuthManager: Validating user credentials', { username: credentials.username });
      
      // 安全检查
      const securityCheck = await this.securityManager.validateLoginAttempt(credentials);
      if (!securityCheck.isValid) {
        return {
          success: false,
          error: securityCheck.reason,
          nextStep: AuthStep.LOGIN
        };
      }
      
      // 验证凭据（这里应该调用实际的认证 API）
      const authResult = await this.performCredentialValidation(credentials);
      
      if (authResult.success) {
        // 更新认证数据
        this.currentAuthData = {
          ...this.currentAuthData,
          credentials,
          loginTime: new Date()
        };
        
        // 记录成功的登录尝试
        await this.securityManager.recordSuccessfulLogin(credentials.username);
        
        return {
          success: true,
          nextStep: AuthStep.GEETEST,
          data: authResult.data
        };
      } else {
        // 记录失败的登录尝试
        await this.securityManager.recordFailedLogin(credentials.username);
        
        return {
          success: false,
          error: authResult.error,
          nextStep: AuthStep.LOGIN
        };
      }
    } catch (error) {
      LOG.error('AuthManager: Error validating credentials', error);
      return {
        success: false,
        error: '认证服务异常，请稍后重试',
        nextStep: AuthStep.LOGIN
      };
    }
  }

  /**
   * 处理极验验证
   */
  async handleGeetestVerification(geetestData: IGeetestData): Promise<boolean> {
    try {
      LOG.info('AuthManager: Processing Geetest verification');
      
      // 验证极验数据
      const isValid = await this.verifyGeetestData(geetestData);
      
      if (isValid) {
        // 更新认证数据
        this.currentAuthData = {
          ...this.currentAuthData,
          geetestData,
          geetestVerified: true
        };
        
        LOG.info('AuthManager: Geetest verification successful');
        return true;
      } else {
        LOG.warn('AuthManager: Geetest verification failed');
        return false;
      }
    } catch (error) {
      LOG.error('AuthManager: Error processing Geetest verification', error);
      return false;
    }
  }

  /**
   * 处理手机号验证
   */
  async handlePhoneVerification(phoneData: IPhoneVerifyData): Promise<boolean> {
    try {
      LOG.info('AuthManager: Processing phone verification', { phoneNumber: phoneData.phoneNumber });
      
      // 验证手机号和验证码
      const isValid = await this.verifyPhoneCode(phoneData);
      
      if (isValid) {
        // 更新认证数据
        this.currentAuthData = {
          ...this.currentAuthData,
          phoneData,
          phoneVerified: true
        };
        
        // 完成认证流程
        await this.completeAuthentication();
        
        LOG.info('AuthManager: Phone verification successful');
        return true;
      } else {
        LOG.warn('AuthManager: Phone verification failed');
        return false;
      }
    } catch (error) {
      LOG.error('AuthManager: Error processing phone verification', error);
      return false;
    }
  }

  /**
   * 获取认证状态
   */
  getAuthStatus(): IAuthStatus {
    return { ...this.authStatus };
  }

  /**
   * 清除认证信息
   */
  async clearAuth(): Promise<void> {
    try {
      LOG.info('AuthManager: Clearing authentication data');
      
      // 清除 Token
      await this.tokenManager.clearTokens();
      
      // 清除认证数据
      this.currentAuthData = {};
      
      // 重置认证状态
      this.authStatus = {
        isAuthenticated: false,
        currentStep: AuthStep.LOGIN,
        user: null,
        lastLoginTime: null,
        deviceId: null
      };
      
      LOG.info('AuthManager: Authentication data cleared');
    } catch (error) {
      LOG.error('AuthManager: Error clearing authentication data', error);
      throw error;
    }
  }

  /**
   * 初始化 IPC 处理器
   */
  private initializeIpcHandlers(): void {
    // 登录请求
    mainIPC.addListener(AUTH_LOGIN_REQUEST, async (credentials: IUserCredentials) => {
      const result = await this.validateCredentials(credentials);
      mainIPC.send({ id: AUTH_LOGIN_RESPONSE, params: result });
    }, EXCLUSIVE_CON);

    // 极验验证请求
    mainIPC.addListener(AUTH_GEETEST_REQUEST, async (geetestData: IGeetestData) => {
      const result = await this.handleGeetestVerification(geetestData);
      mainIPC.send({ id: AUTH_GEETEST_RESPONSE, params: result });
    }, EXCLUSIVE_CON);

    // 手机验证请求
    mainIPC.addListener(AUTH_PHONE_VERIFY_REQUEST, async (phoneData: IPhoneVerifyData) => {
      const result = await this.handlePhoneVerification(phoneData);
      mainIPC.send({ id: AUTH_PHONE_VERIFY_RESPONSE, params: result });
    }, EXCLUSIVE_CON);

    // 认证状态请求
    mainIPC.addListener(AUTH_STATUS_REQUEST, () => {
      const status = this.getAuthStatus();
      mainIPC.send({ id: AUTH_STATUS_RESPONSE, params: status });
    }, EXCLUSIVE_CON);

    // 清除认证请求
    mainIPC.addListener(AUTH_CLEAR_REQUEST, async () => {
      await this.clearAuth();
      mainIPC.send({ id: AUTH_CLEAR_RESPONSE, params: { success: true } });
    }, EXCLUSIVE_CON);
  }

  /**
   * 检查现有认证状态
   */
  private async checkExistingAuth(): Promise<void> {
    try {
      const token = await this.tokenManager.getValidToken();
      if (token) {
        // 验证 Token 有效性
        const isValid = await this.tokenManager.validateToken(token);
        if (isValid) {
          this.authStatus.isAuthenticated = true;
          this.authStatus.currentStep = AuthStep.SUCCESS;
          LOG.info('AuthManager: Found valid existing authentication');
        }
      }
    } catch (error) {
      LOG.warn('AuthManager: No valid existing authentication found', error);
    }
  }

  /**
   * 执行凭据验证（实际的 API 调用）
   */
  private async performCredentialValidation(credentials: IUserCredentials): Promise<IAuthResult> {
    // TODO: 实现实际的认证 API 调用
    // 这里应该调用后端认证服务
    
    // 模拟认证逻辑
    if (credentials.username && credentials.password) {
      return {
        success: true,
        data: {
          userId: 'mock_user_id',
          username: credentials.username
        }
      };
    } else {
      return {
        success: false,
        error: '用户名或密码不能为空'
      };
    }
  }

  /**
   * 验证极验数据
   */
  private async verifyGeetestData(geetestData: IGeetestData): Promise<boolean> {
    // TODO: 实现极验验证逻辑
    // 这里应该调用极验验证 API
    
    // 模拟验证逻辑
    return geetestData.geetest_challenge && geetestData.geetest_validate && geetestData.geetest_seccode;
  }

  /**
   * 验证手机验证码
   */
  private async verifyPhoneCode(phoneData: IPhoneVerifyData): Promise<boolean> {
    // TODO: 实现手机验证码验证逻辑
    // 这里应该调用短信验证 API
    
    // 模拟验证逻辑
    return phoneData.phoneNumber && phoneData.verificationCode && phoneData.verificationCode.length === 6;
  }

  /**
   * 完成认证流程
   */
  private async completeAuthentication(): Promise<void> {
    try {
      // 生成和保存 Token
      const token = await this.tokenManager.generateToken(this.currentAuthData);
      await this.tokenManager.saveToken(token);
      
      // 更新认证状态
      this.authStatus = {
        isAuthenticated: true,
        currentStep: AuthStep.SUCCESS,
        user: {
          id: this.currentAuthData.credentials?.username || '',
          username: this.currentAuthData.credentials?.username || '',
          // TODO: 添加更多用户信息
        },
        lastLoginTime: new Date(),
        deviceId: await this.securityManager.getDeviceId()
      };
      
      LOG.info('AuthManager: Authentication completed successfully');
    } catch (error) {
      LOG.error('AuthManager: Error completing authentication', error);
      throw error;
    }
  }
}

// 导出单例实例
export const authManager = new AuthManager();
