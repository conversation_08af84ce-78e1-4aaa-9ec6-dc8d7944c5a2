import * as crypto from 'crypto';
import * as fs from 'fs-extra';
import * as path from 'path';
import * as os from 'os';
import { app } from 'electron';
import LOG from '@htElectronSDK/main/log';
import { IUserCredentials, ISecurityManager, ISecurityCheck, ILoginAttempt } from '../types/auth';

/**
 * 安全管理器
 * 负责登录安全控制、设备识别、异常检测等
 */
export class SecurityManager implements ISecurityManager {
  private readonly SECURITY_FILE_NAME = 'security_data.json';
  private readonly MAX_LOGIN_ATTEMPTS = 5;
  private readonly LOCKOUT_DURATION = 15 * 60 * 1000; // 15分钟
  private readonly ATTEMPT_WINDOW = 60 * 60 * 1000; // 1小时窗口期
  
  private securityFilePath: string;
  private deviceId: string | null = null;
  private loginAttempts: Map<string, ILoginAttempt[]> = new Map();

  constructor() {
    this.securityFilePath = path.join(app.getPath('userData'), this.SECURITY_FILE_NAME);
  }

  /**
   * 初始化安全管理器
   */
  async initialize(): Promise<void> {
    try {
      LOG.info('SecurityManager: Initializing security manager');
      
      // 生成或获取设备 ID
      this.deviceId = await this.generateDeviceId();
      
      // 加载历史登录尝试记录
      await this.loadLoginAttempts();
      
      // 清理过期的登录尝试记录
      this.cleanupExpiredAttempts();
      
      LOG.info('SecurityManager: Security manager initialized successfully', { deviceId: this.deviceId });
    } catch (error) {
      LOG.error('SecurityManager: Failed to initialize security manager', error);
      throw error;
    }
  }

  /**
   * 验证登录尝试
   */
  async validateLoginAttempt(credentials: IUserCredentials): Promise<ISecurityCheck> {
    try {
      const username = credentials.username;
      const currentTime = Date.now();
      
      // 获取用户的登录尝试记录
      const userAttempts = this.loginAttempts.get(username) || [];
      
      // 清理过期的尝试记录
      const validAttempts = userAttempts.filter(
        attempt => currentTime - attempt.timestamp < this.ATTEMPT_WINDOW
      );
      
      // 检查是否被锁定
      const lastFailedAttempt = validAttempts
        .filter(attempt => !attempt.success)
        .sort((a, b) => b.timestamp - a.timestamp)[0];
      
      if (lastFailedAttempt && 
          validAttempts.filter(attempt => !attempt.success).length >= this.MAX_LOGIN_ATTEMPTS &&
          currentTime - lastFailedAttempt.timestamp < this.LOCKOUT_DURATION) {
        
        const remainingLockTime = this.LOCKOUT_DURATION - (currentTime - lastFailedAttempt.timestamp);
        const remainingMinutes = Math.ceil(remainingLockTime / (60 * 1000));
        
        LOG.warn('SecurityManager: Login attempt blocked due to lockout', { 
          username, 
          remainingMinutes 
        });
        
        return {
          isValid: false,
          reason: `账户已被锁定，请在 ${remainingMinutes} 分钟后重试`,
          riskLevel: 'HIGH'
        };
      }
      
      // 检查异常登录模式
      const riskLevel = this.assessRiskLevel(username, validAttempts);
      
      // 检查设备安全性
      const deviceCheck = await this.validateDevice();
      if (!deviceCheck.isValid) {
        return deviceCheck;
      }
      
      // 检查凭据格式
      const credentialCheck = this.validateCredentialFormat(credentials);
      if (!credentialCheck.isValid) {
        return credentialCheck;
      }
      
      return {
        isValid: true,
        riskLevel,
        deviceId: this.deviceId!
      };
      
    } catch (error) {
      LOG.error('SecurityManager: Error validating login attempt', error);
      return {
        isValid: false,
        reason: '安全验证失败，请稍后重试',
        riskLevel: 'HIGH'
      };
    }
  }

  /**
   * 记录成功的登录
   */
  async recordSuccessfulLogin(username: string): Promise<void> {
    try {
      const attempt: ILoginAttempt = {
        username,
        timestamp: Date.now(),
        success: true,
        deviceId: this.deviceId!,
        ipAddress: await this.getLocalIpAddress(),
        userAgent: this.getUserAgent()
      };
      
      this.addLoginAttempt(username, attempt);
      await this.saveLoginAttempts();
      
      LOG.info('SecurityManager: Successful login recorded', { username });
    } catch (error) {
      LOG.error('SecurityManager: Error recording successful login', error);
    }
  }

  /**
   * 记录失败的登录
   */
  async recordFailedLogin(username: string, reason?: string): Promise<void> {
    try {
      const attempt: ILoginAttempt = {
        username,
        timestamp: Date.now(),
        success: false,
        deviceId: this.deviceId!,
        ipAddress: await this.getLocalIpAddress(),
        userAgent: this.getUserAgent(),
        failureReason: reason
      };
      
      this.addLoginAttempt(username, attempt);
      await this.saveLoginAttempts();
      
      const userAttempts = this.loginAttempts.get(username) || [];
      const recentFailures = userAttempts.filter(
        a => !a.success && Date.now() - a.timestamp < this.ATTEMPT_WINDOW
      ).length;
      
      LOG.warn('SecurityManager: Failed login recorded', { 
        username, 
        recentFailures,
        reason 
      });
      
      // 如果失败次数过多，记录安全事件
      if (recentFailures >= this.MAX_LOGIN_ATTEMPTS - 1) {
        await this.recordSecurityEvent('MULTIPLE_FAILED_LOGINS', {
          username,
          failureCount: recentFailures,
          deviceId: this.deviceId
        });
      }
      
    } catch (error) {
      LOG.error('SecurityManager: Error recording failed login', error);
    }
  }

  /**
   * 获取设备 ID
   */
  async getDeviceId(): Promise<string> {
    if (!this.deviceId) {
      this.deviceId = await this.generateDeviceId();
    }
    return this.deviceId;
  }

  /**
   * 检查设备是否可信
   */
  async isDeviceTrusted(): Promise<boolean> {
    try {
      // 检查设备是否在可信设备列表中
      const trustedDevices = await this.getTrustedDevices();
      return trustedDevices.includes(this.deviceId!);
    } catch (error) {
      LOG.error('SecurityManager: Error checking device trust', error);
      return false;
    }
  }

  /**
   * 添加可信设备
   */
  async addTrustedDevice(deviceId?: string): Promise<void> {
    try {
      const targetDeviceId = deviceId || this.deviceId!;
      const trustedDevices = await this.getTrustedDevices();
      
      if (!trustedDevices.includes(targetDeviceId)) {
        trustedDevices.push(targetDeviceId);
        await this.saveTrustedDevices(trustedDevices);
        LOG.info('SecurityManager: Device added to trusted list', { deviceId: targetDeviceId });
      }
    } catch (error) {
      LOG.error('SecurityManager: Error adding trusted device', error);
    }
  }

  /**
   * 生成设备 ID
   */
  private async generateDeviceId(): Promise<string> {
    try {
      // 收集设备特征信息
      const deviceInfo = {
        hostname: os.hostname(),
        platform: os.platform(),
        arch: os.arch(),
        release: os.release(),
        cpus: os.cpus().length,
        totalmem: os.totalmem(),
        appPath: app.getAppPath(),
        version: app.getVersion()
      };
      
      // 生成设备指纹
      const deviceFingerprint = JSON.stringify(deviceInfo);
      const deviceId = crypto
        .createHash('sha256')
        .update(deviceFingerprint)
        .digest('hex')
        .substring(0, 32);
      
      LOG.info('SecurityManager: Device ID generated', { deviceId });
      return deviceId;
    } catch (error) {
      LOG.error('SecurityManager: Error generating device ID', error);
      // 生成随机设备 ID 作为后备
      return crypto.randomBytes(16).toString('hex');
    }
  }

  /**
   * 验证设备
   */
  private async validateDevice(): Promise<ISecurityCheck> {
    try {
      // 检查设备 ID 是否有效
      if (!this.deviceId) {
        return {
          isValid: false,
          reason: '设备识别失败',
          riskLevel: 'HIGH'
        };
      }
      
      // 检查设备环境
      const isSecureEnvironment = await this.checkSecureEnvironment();
      if (!isSecureEnvironment) {
        return {
          isValid: false,
          reason: '检测到不安全的运行环境',
          riskLevel: 'HIGH'
        };
      }
      
      return {
        isValid: true,
        riskLevel: 'LOW'
      };
    } catch (error) {
      LOG.error('SecurityManager: Error validating device', error);
      return {
        isValid: false,
        reason: '设备验证异常',
        riskLevel: 'HIGH'
      };
    }
  }

  /**
   * 验证凭据格式
   */
  private validateCredentialFormat(credentials: IUserCredentials): ISecurityCheck {
    // 检查用户名格式
    if (!credentials.username || credentials.username.length < 3) {
      return {
        isValid: false,
        reason: '用户名格式不正确',
        riskLevel: 'MEDIUM'
      };
    }
    
    // 检查密码格式
    if (!credentials.password || credentials.password.length < 6) {
      return {
        isValid: false,
        reason: '密码格式不正确',
        riskLevel: 'MEDIUM'
      };
    }
    
    // 检查是否包含恶意字符
    const maliciousPatterns = [
      /<script/i,
      /javascript:/i,
      /on\w+\s*=/i,
      /\beval\s*\(/i
    ];
    
    const inputText = credentials.username + credentials.password;
    for (const pattern of maliciousPatterns) {
      if (pattern.test(inputText)) {
        return {
          isValid: false,
          reason: '检测到恶意输入',
          riskLevel: 'HIGH'
        };
      }
    }
    
    return {
      isValid: true,
      riskLevel: 'LOW'
    };
  }

  /**
   * 评估风险等级
   */
  private assessRiskLevel(username: string, attempts: ILoginAttempt[]): 'LOW' | 'MEDIUM' | 'HIGH' {
    const failedAttempts = attempts.filter(a => !a.success);
    const successfulAttempts = attempts.filter(a => a.success);
    
    // 如果有多次失败尝试
    if (failedAttempts.length >= 3) {
      return 'HIGH';
    }
    
    // 如果是新设备
    if (successfulAttempts.length === 0) {
      return 'MEDIUM';
    }
    
    // 检查时间模式异常
    const now = Date.now();
    const recentAttempts = attempts.filter(a => now - a.timestamp < 5 * 60 * 1000); // 5分钟内
    if (recentAttempts.length > 3) {
      return 'HIGH';
    }
    
    return 'LOW';
  }

  /**
   * 添加登录尝试记录
   */
  private addLoginAttempt(username: string, attempt: ILoginAttempt): void {
    const userAttempts = this.loginAttempts.get(username) || [];
    userAttempts.push(attempt);
    
    // 只保留最近的尝试记录
    const maxAttempts = 50;
    if (userAttempts.length > maxAttempts) {
      userAttempts.splice(0, userAttempts.length - maxAttempts);
    }
    
    this.loginAttempts.set(username, userAttempts);
  }

  /**
   * 加载登录尝试记录
   */
  private async loadLoginAttempts(): Promise<void> {
    try {
      if (await fs.pathExists(this.securityFilePath)) {
        const data = await fs.readJson(this.securityFilePath);
        if (data.loginAttempts) {
          this.loginAttempts = new Map(Object.entries(data.loginAttempts));
        }
      }
    } catch (error) {
      LOG.warn('SecurityManager: Error loading login attempts', error);
    }
  }

  /**
   * 保存登录尝试记录
   */
  private async saveLoginAttempts(): Promise<void> {
    try {
      const data = {
        loginAttempts: Object.fromEntries(this.loginAttempts),
        lastUpdated: Date.now()
      };
      await fs.writeJson(this.securityFilePath, data);
    } catch (error) {
      LOG.error('SecurityManager: Error saving login attempts', error);
    }
  }

  /**
   * 清理过期的尝试记录
   */
  private cleanupExpiredAttempts(): void {
    const currentTime = Date.now();
    const maxAge = 7 * 24 * 60 * 60 * 1000; // 7天
    
    for (const [username, attempts] of this.loginAttempts.entries()) {
      const validAttempts = attempts.filter(
        attempt => currentTime - attempt.timestamp < maxAge
      );
      
      if (validAttempts.length === 0) {
        this.loginAttempts.delete(username);
      } else {
        this.loginAttempts.set(username, validAttempts);
      }
    }
  }

  /**
   * 获取本地 IP 地址
   */
  private async getLocalIpAddress(): Promise<string> {
    try {
      const networkInterfaces = os.networkInterfaces();
      for (const interfaceName in networkInterfaces) {
        const interfaces = networkInterfaces[interfaceName];
        if (interfaces) {
          for (const iface of interfaces) {
            if (iface.family === 'IPv4' && !iface.internal) {
              return iface.address;
            }
          }
        }
      }
      return '127.0.0.1';
    } catch (error) {
      return '127.0.0.1';
    }
  }

  /**
   * 获取用户代理信息
   */
  private getUserAgent(): string {
    return `LinkFlow/${app.getVersion()} (${os.platform()} ${os.arch()})`;
  }

  /**
   * 检查安全环境
   */
  private async checkSecureEnvironment(): Promise<boolean> {
    try {
      // 检查是否在调试模式
      if (process.env.NODE_ENV === 'development') {
        return true; // 开发环境允许
      }
      
      // 检查是否有调试器附加
      if (process.debugPort) {
        LOG.warn('SecurityManager: Debugger detected');
        return false;
      }
      
      return true;
    } catch (error) {
      LOG.error('SecurityManager: Error checking secure environment', error);
      return false;
    }
  }

  /**
   * 获取可信设备列表
   */
  private async getTrustedDevices(): Promise<string[]> {
    try {
      if (await fs.pathExists(this.securityFilePath)) {
        const data = await fs.readJson(this.securityFilePath);
        return data.trustedDevices || [];
      }
      return [];
    } catch (error) {
      LOG.error('SecurityManager: Error getting trusted devices', error);
      return [];
    }
  }

  /**
   * 保存可信设备列表
   */
  private async saveTrustedDevices(devices: string[]): Promise<void> {
    try {
      let data = {};
      if (await fs.pathExists(this.securityFilePath)) {
        data = await fs.readJson(this.securityFilePath);
      }
      
      data = { ...data, trustedDevices: devices };
      await fs.writeJson(this.securityFilePath, data);
    } catch (error) {
      LOG.error('SecurityManager: Error saving trusted devices', error);
    }
  }

  /**
   * 记录安全事件
   */
  private async recordSecurityEvent(eventType: string, details: any): Promise<void> {
    try {
      const event = {
        type: eventType,
        timestamp: Date.now(),
        deviceId: this.deviceId,
        details
      };
      
      LOG.warn('SecurityManager: Security event recorded', event);
      
      // 这里可以添加上报到安全监控系统的逻辑
    } catch (error) {
      LOG.error('SecurityManager: Error recording security event', error);
    }
  }
}
