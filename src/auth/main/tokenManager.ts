import * as crypto from 'crypto';
import * as fs from 'fs-extra';
import * as path from 'path';
import { app } from 'electron';
import LOG from '@htElectronSDK/main/log';
import { IAuthData, ITokenData, ITokenManager } from '../types/auth';

/**
 * Token 管理器
 * 负责 Token 的生成、存储、验证和刷新
 */
export class TokenManager implements ITokenManager {
  private readonly TOKEN_FILE_NAME = 'auth_tokens.json';
  private readonly ENCRYPTION_KEY_LENGTH = 32;
  private readonly IV_LENGTH = 16;
  private tokenFilePath: string;
  private encryptionKey: Buffer;

  constructor() {
    this.tokenFilePath = path.join(app.getPath('userData'), this.TOKEN_FILE_NAME);
    this.encryptionKey = this.generateEncryptionKey();
  }

  /**
   * 初始化 Token 管理器
   */
  async initialize(): Promise<void> {
    try {
      LOG.info('TokenManager: Initializing token manager');
      
      // 确保用户数据目录存在
      const userDataPath = app.getPath('userData');
      await fs.ensureDir(userDataPath);
      
      // 清理过期的 Token
      await this.cleanupExpiredTokens();
      
      LOG.info('TokenManager: Token manager initialized successfully');
    } catch (error) {
      LOG.error('TokenManager: Failed to initialize token manager', error);
      throw error;
    }
  }

  /**
   * 生成 Token
   */
  async generateToken(authData: Partial<IAuthData>): Promise<string> {
    try {
      const tokenData: ITokenData = {
        userId: authData.credentials?.username || '',
        username: authData.credentials?.username || '',
        issuedAt: Date.now(),
        expiresAt: Date.now() + (24 * 60 * 60 * 1000), // 24小时过期
        deviceId: await this.getDeviceId(),
        sessionId: this.generateSessionId(),
        permissions: this.getDefaultPermissions()
      };

      // 生成 JWT 风格的 Token
      const header = {
        alg: 'HS256',
        typ: 'JWT'
      };

      const payload = {
        ...tokenData,
        iat: Math.floor(tokenData.issuedAt / 1000),
        exp: Math.floor(tokenData.expiresAt / 1000)
      };

      const headerBase64 = Buffer.from(JSON.stringify(header)).toString('base64url');
      const payloadBase64 = Buffer.from(JSON.stringify(payload)).toString('base64url');
      
      const signature = this.generateSignature(`${headerBase64}.${payloadBase64}`);
      const token = `${headerBase64}.${payloadBase64}.${signature}`;

      LOG.info('TokenManager: Token generated successfully', { userId: tokenData.userId });
      return token;
    } catch (error) {
      LOG.error('TokenManager: Error generating token', error);
      throw error;
    }
  }

  /**
   * 保存 Token
   */
  async saveToken(token: string): Promise<void> {
    try {
      const tokenData = this.parseToken(token);
      if (!tokenData) {
        throw new Error('Invalid token format');
      }

      // 加密 Token 数据
      const encryptedData = this.encryptData(JSON.stringify({
        token,
        tokenData,
        savedAt: Date.now()
      }));

      // 保存到文件
      await fs.writeFile(this.tokenFilePath, encryptedData);
      
      LOG.info('TokenManager: Token saved successfully', { userId: tokenData.userId });
    } catch (error) {
      LOG.error('TokenManager: Error saving token', error);
      throw error;
    }
  }

  /**
   * 获取有效的 Token
   */
  async getValidToken(): Promise<string | null> {
    try {
      if (!await fs.pathExists(this.tokenFilePath)) {
        return null;
      }

      // 读取并解密 Token 文件
      const encryptedData = await fs.readFile(this.tokenFilePath);
      const decryptedData = this.decryptData(encryptedData);
      const tokenInfo = JSON.parse(decryptedData);

      const { token, tokenData } = tokenInfo;

      // 检查 Token 是否过期
      if (Date.now() > tokenData.expiresAt) {
        LOG.info('TokenManager: Token expired, removing');
        await this.clearTokens();
        return null;
      }

      // 验证 Token 完整性
      const isValid = await this.validateToken(token);
      if (!isValid) {
        LOG.warn('TokenManager: Token validation failed, removing');
        await this.clearTokens();
        return null;
      }

      LOG.info('TokenManager: Valid token retrieved', { userId: tokenData.userId });
      return token;
    } catch (error) {
      LOG.error('TokenManager: Error retrieving token', error);
      return null;
    }
  }

  /**
   * 验证 Token
   */
  async validateToken(token: string): Promise<boolean> {
    try {
      const parts = token.split('.');
      if (parts.length !== 3) {
        return false;
      }

      const [headerBase64, payloadBase64, signature] = parts;
      
      // 验证签名
      const expectedSignature = this.generateSignature(`${headerBase64}.${payloadBase64}`);
      if (signature !== expectedSignature) {
        LOG.warn('TokenManager: Token signature validation failed');
        return false;
      }

      // 解析 payload
      const payload = JSON.parse(Buffer.from(payloadBase64, 'base64url').toString());
      
      // 检查过期时间
      if (Date.now() > payload.exp * 1000) {
        LOG.info('TokenManager: Token expired during validation');
        return false;
      }

      // 检查设备 ID
      const currentDeviceId = await this.getDeviceId();
      if (payload.deviceId !== currentDeviceId) {
        LOG.warn('TokenManager: Token device ID mismatch');
        return false;
      }

      return true;
    } catch (error) {
      LOG.error('TokenManager: Error validating token', error);
      return false;
    }
  }

  /**
   * 刷新 Token
   */
  async refreshToken(token: string): Promise<string | null> {
    try {
      const tokenData = this.parseToken(token);
      if (!tokenData) {
        return null;
      }

      // 检查是否在刷新窗口期内（过期前1小时）
      const refreshWindow = 60 * 60 * 1000; // 1小时
      if (Date.now() > tokenData.expiresAt - refreshWindow) {
        // 生成新的 Token
        const newTokenData: ITokenData = {
          ...tokenData,
          issuedAt: Date.now(),
          expiresAt: Date.now() + (24 * 60 * 60 * 1000), // 新的24小时过期时间
          sessionId: this.generateSessionId()
        };

        const newToken = await this.generateTokenFromData(newTokenData);
        await this.saveToken(newToken);
        
        LOG.info('TokenManager: Token refreshed successfully', { userId: tokenData.userId });
        return newToken;
      }

      return token; // Token 还不需要刷新
    } catch (error) {
      LOG.error('TokenManager: Error refreshing token', error);
      return null;
    }
  }

  /**
   * 清除所有 Token
   */
  async clearTokens(): Promise<void> {
    try {
      if (await fs.pathExists(this.tokenFilePath)) {
        await fs.remove(this.tokenFilePath);
        LOG.info('TokenManager: All tokens cleared');
      }
    } catch (error) {
      LOG.error('TokenManager: Error clearing tokens', error);
      throw error;
    }
  }

  /**
   * 解析 Token
   */
  private parseToken(token: string): ITokenData | null {
    try {
      const parts = token.split('.');
      if (parts.length !== 3) {
        return null;
      }

      const payload = JSON.parse(Buffer.from(parts[1], 'base64url').toString());
      return {
        userId: payload.userId,
        username: payload.username,
        issuedAt: payload.iat * 1000,
        expiresAt: payload.exp * 1000,
        deviceId: payload.deviceId,
        sessionId: payload.sessionId,
        permissions: payload.permissions
      };
    } catch (error) {
      LOG.error('TokenManager: Error parsing token', error);
      return null;
    }
  }

  /**
   * 从 Token 数据生成 Token
   */
  private async generateTokenFromData(tokenData: ITokenData): Promise<string> {
    const header = {
      alg: 'HS256',
      typ: 'JWT'
    };

    const payload = {
      ...tokenData,
      iat: Math.floor(tokenData.issuedAt / 1000),
      exp: Math.floor(tokenData.expiresAt / 1000)
    };

    const headerBase64 = Buffer.from(JSON.stringify(header)).toString('base64url');
    const payloadBase64 = Buffer.from(JSON.stringify(payload)).toString('base64url');
    
    const signature = this.generateSignature(`${headerBase64}.${payloadBase64}`);
    return `${headerBase64}.${payloadBase64}.${signature}`;
  }

  /**
   * 生成签名
   */
  private generateSignature(data: string): string {
    return crypto
      .createHmac('sha256', this.encryptionKey)
      .update(data)
      .digest('base64url');
  }

  /**
   * 生成会话 ID
   */
  private generateSessionId(): string {
    return crypto.randomBytes(16).toString('hex');
  }

  /**
   * 获取设备 ID
   */
  private async getDeviceId(): Promise<string> {
    // 基于机器特征生成设备 ID
    const machineId = require('os').hostname() + require('os').platform() + require('os').arch();
    return crypto.createHash('sha256').update(machineId).digest('hex');
  }

  /**
   * 获取默认权限
   */
  private getDefaultPermissions(): string[] {
    return ['read', 'write', 'upload', 'download'];
  }

  /**
   * 生成加密密钥
   */
  private generateEncryptionKey(): Buffer {
    // 在实际应用中，这个密钥应该从安全的地方获取
    const keyMaterial = app.getPath('userData') + app.getName() + app.getVersion();
    return crypto.scryptSync(keyMaterial, 'salt', this.ENCRYPTION_KEY_LENGTH);
  }

  /**
   * 加密数据
   */
  private encryptData(data: string): Buffer {
    const iv = crypto.randomBytes(this.IV_LENGTH);
    const cipher = crypto.createCipher('aes-256-cbc', this.encryptionKey);
    cipher.setAutoPadding(true);
    
    let encrypted = cipher.update(data, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    return Buffer.concat([iv, Buffer.from(encrypted, 'hex')]);
  }

  /**
   * 解密数据
   */
  private decryptData(encryptedData: Buffer): string {
    const iv = encryptedData.slice(0, this.IV_LENGTH);
    const encrypted = encryptedData.slice(this.IV_LENGTH);
    
    const decipher = crypto.createDecipher('aes-256-cbc', this.encryptionKey);
    decipher.setAutoPadding(true);
    
    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    
    return decrypted;
  }

  /**
   * 清理过期的 Token
   */
  private async cleanupExpiredTokens(): Promise<void> {
    try {
      const token = await this.getValidToken();
      if (!token) {
        // 如果没有有效的 Token，清理文件
        await this.clearTokens();
      }
    } catch (error) {
      LOG.warn('TokenManager: Error during token cleanup', error);
    }
  }
}
