/**
 * 主进程认证模块入口文件
 */

import LOG from '@htElectronSDK/main/log';
import { authManager } from './authManager';

/**
 * 初始化认证系统
 */
export const initAuthSystem = async (): Promise<void> => {
  try {
    LOG.info('AuthSystem: Initializing authentication system');
    
    // 初始化认证管理器
    await authManager.initialize();
    
    LOG.info('AuthSystem: Authentication system initialized successfully');
  } catch (error) {
    LOG.error('AuthSystem: Failed to initialize authentication system', error);
    throw error;
  }
};

/**
 * 清理认证系统
 */
export const cleanupAuthSystem = async (): Promise<void> => {
  try {
    LOG.info('AuthSystem: Cleaning up authentication system');
    
    // 清除认证数据
    await authManager.clearAuth();
    
    LOG.info('AuthSystem: Authentication system cleaned up successfully');
  } catch (error) {
    LOG.error('AuthSystem: Error cleaning up authentication system', error);
  }
};

// 导出认证管理器实例
export { authManager } from './authManager';
export { TokenManager } from './tokenManager';
export { SecurityManager } from './securityManager';
