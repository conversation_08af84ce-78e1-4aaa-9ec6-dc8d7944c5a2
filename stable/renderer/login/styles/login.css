/**
 * 登录模块样式文件
 */

/* 登录应用容器 */
.login-app {
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
    sans-serif;
}

.login-container {
  background: rgba(255, 255, 255, 95%);
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 10%);
  padding: 40px;
  width: 100%;
  max-width: 380px;
  backdrop-filter: blur(10px);
  margin: 0 auto;
}

/* 登录头部 */
.login-header {
  text-align: center;
  margin-bottom: 30px;
}

.login-header h1 {
  color: #333;
  font-size: 28px;
  font-weight: 600;
  margin: 0 0 8px;
}

.login-header p {
  color: #666;
  font-size: 14px;
  margin: 0;
}

/* 登录内容区域 */
.login-content {
  margin-bottom: 20px;
}

/* 登录表单 */
.login-form {
  width: 100%;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  color: #333;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 6px;
}

.form-group input[type="text"],
.form-group input[type="password"] {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.3s, box-shadow 0.3s;
  box-sizing: border-box;
}

.form-group input[type="text"]:focus,
.form-group input[type="password"]:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 10%);
}

.form-group input.error {
  border-color: #e74c3c;
}

.error-text {
  color: #e74c3c;
  font-size: 12px;
  margin-top: 4px;
  display: block;
}

/* 复选框组 */
.checkbox-group {
  margin-bottom: 24px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 14px;
  color: #666;
}

.checkbox-label input[type="checkbox"] {
  margin-right: 8px;
  width: 16px;
  height: 16px;
}

.checkbox-text {
  user-select: none;
}

/* 错误消息 */
.error-message {
  background: #ffeaea;
  color: #e74c3c;
  padding: 12px;
  border-radius: 6px;
  font-size: 14px;
  margin-bottom: 20px;
  border: 1px solid #f5c6cb;
}

/* 登录按钮 */
.login-btn {
  width: 100%;
  background: #667eea;
  color: white;
  border: none;
  padding: 14px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s, transform 0.1s;
}

.login-btn:hover:not(:disabled) {
  background: #5a6fd8;
  transform: translateY(-1px);
}

.login-btn:active:not(:disabled) {
  transform: translateY(0);
}

.login-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
}

/* 表单链接 */
.form-links {
  text-align: center;
  margin-top: 20px;
}

.forgot-password {
  color: #667eea;
  text-decoration: none;
  font-size: 14px;
  transition: color 0.3s;
}

.forgot-password:hover {
  color: #5a6fd8;
  text-decoration: underline;
}

/* 加载动画 */
.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
}

.spinner {
  display: flex;
  gap: 4px;
  margin-bottom: 16px;
}

.spinner-circle {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #667eea;
  animation: spinner-bounce 1.4s ease-in-out infinite both;
}

.spinner-circle:nth-child(1) {
  animation-delay: -0.32s;
}

.spinner-circle:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes spinner-bounce {
  0%,
  80%,
  100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

.loading-message {
  color: #666;
  font-size: 14px;
  margin: 0;
}

/* 尺寸变体 */
.loading-spinner.small .spinner-circle {
  width: 6px;
  height: 6px;
}

.loading-spinner.large .spinner-circle {
  width: 12px;
  height: 12px;
}

/* 验证容器 */
.captcha-container,
.phone-verify-container {
  text-align: center;
  padding: 20px;
}

.captcha-container h3,
.phone-verify-container h3 {
  color: #333;
  margin-bottom: 8px;
}

.captcha-container p,
.phone-verify-container p {
  color: #666;
  font-size: 14px;
  margin-bottom: 20px;
}

.captcha-widget,
.phone-widget {
  margin: 20px 0;
  min-height: 100px;
  border: 2px dashed #e1e5e9;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 14px;
}

.back-btn {
  background: transparent;
  color: #667eea;
  border: 2px solid #667eea;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s;
}

.back-btn:hover {
  background: #667eea;
  color: white;
}

/* 登录底部 */
.login-footer {
  text-align: center;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #e1e5e9;
}

.login-footer p {
  color: #999;
  font-size: 12px;
  margin: 0;
}

/* 手机验证组件样式 */
.verify-code-input {
  display: flex;
  gap: 8px;
}

.verify-code-input input {
  flex: 1;
}

.send-code-btn {
  background: #667eea;
  color: white;
  border: none;
  padding: 12px 16px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  white-space: nowrap;
  transition: background-color 0.3s;
}

.send-code-btn:hover:not(:disabled) {
  background: #5a6fd8;
}

.send-code-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.phone-verify-actions {
  display: flex;
  gap: 12px;
  margin-top: 20px;
}

.verify-btn {
  flex: 1;
  background: #667eea;
  color: white;
  border: none;
  padding: 12px;
  border-radius: 6px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.verify-btn:hover:not(:disabled) {
  background: #5a6fd8;
}

.verify-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .login-container {
    margin: 20px;
    padding: 30px 20px;
  }

  .login-header h1 {
    font-size: 24px;
  }

  .verify-code-input {
    flex-direction: column;
  }

  .phone-verify-actions {
    flex-direction: column;
  }
}
