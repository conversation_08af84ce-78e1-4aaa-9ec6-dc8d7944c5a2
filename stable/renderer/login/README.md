# 登录模块文档

## 概述

这是一个基于React的模块化登录系统，支持多种验证方式和窗口尺寸自适应。

## 文件结构

```
stable/renderer/login/
├── index.tsx                 # 登录模块入口
├── components/              # React组件
│   ├── LoginApp.tsx         # 主登录应用组件
│   ├── LoginForm.tsx        # 基础登录表单
│   ├── LoadingSpinner.tsx   # 加载动画组件
│   ├── CaptchaVerify.tsx    # 极验验证组件（预留）
│   └── PhoneVerify.tsx      # 手机验证组件（预留）
├── services/               # 业务逻辑服务
│   └── LoginManager.ts     # 登录管理器
├── styles/                 # 样式文件
│   └── login.css          # 登录模块样式
└── README.md              # 文档
```

## 主要功能

### 1. 基础登录
- 用户名/密码登录
- 记住我功能
- 表单验证
- 错误处理

### 2. 多步验证（预留接口）
- 极验验证
- 手机号验证
- 可扩展其他验证方式

### 3. 窗口尺寸管理
- 登录时使用小窗口（450x600）
- 登录成功后切换到业务窗口（1500x980）
- 自动居中显示

## 使用方法

### 基本使用

登录模块会自动初始化，无需手动调用。应用启动时会：

1. 检查登录状态
2. 如果未登录，显示登录页面（小窗口）
3. 登录成功后，切换到业务窗口并跳转到目标页面

### 自定义登录逻辑

修改 `services/LoginManager.ts` 中的方法：

```typescript
// 替换模拟API为真实API调用
async performLogin(username: string, password: string): Promise<LoginResult> {
  try {
    const response = await fetch('/api/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ username, password })
    });
    
    const data = await response.json();
    
    if (data.success) {
      return {
        success: true,
        token: data.token
      };
    } else {
      return {
        success: false,
        message: data.message,
        needCaptcha: data.needCaptcha,
        needPhoneVerify: data.needPhoneVerify
      };
    }
  } catch (error) {
    return {
      success: false,
      message: '网络错误，请稍后重试'
    };
  }
}
```

### 集成极验验证

1. 安装极验SDK
2. 修改 `components/CaptchaVerify.tsx`
3. 在 `initGeetestCaptcha` 方法中集成SDK

### 集成手机验证

1. 修改 `components/PhoneVerify.tsx`
2. 实现 `sendVerificationCode` 和 `verifyPhoneCode` 方法
3. 连接后端API

## API接口

### LoginManager

#### 方法

- `performLogin(username, password)` - 执行基础登录
- `performLoginWithCaptcha(loginData, captchaData)` - 带极验的登录
- `performLoginWithPhone(loginData, phoneData)` - 带手机验证的登录
- `checkLoginStatus()` - 检查登录状态
- `saveUserSession(token, username)` - 保存用户会话
- `logout()` - 登出
- `getCurrentUser()` - 获取当前用户信息

#### 返回格式

```typescript
interface LoginResult {
  success: boolean;
  token?: string;
  message?: string;
  needCaptcha?: boolean;
  needPhoneVerify?: boolean;
}
```

### 窗口尺寸控制

通过IPC通信控制窗口尺寸：

```typescript
// 切换到业务窗口尺寸
(window as any).electronAPI.ipcSend('switch-to-business-size');

// 切换到登录窗口尺寸
(window as any).electronAPI.ipcSend('switch-to-login-size');
```

## 样式定制

修改 `styles/login.css` 来定制登录页面样式：

- `.login-app` - 整体容器
- `.login-container` - 登录框
- `.login-form` - 表单样式
- `.loading-spinner` - 加载动画

## 测试账号

开发环境下可使用以下测试账号：

- 普通登录：`admin` / `password`
- 需要极验：`captcha` / `test`
- 需要手机验证：`phone` / `test`

## 注意事项

1. 确保已安装React依赖：`npm install react react-dom`
2. 极验和手机验证组件需要额外的SDK集成
3. 窗口尺寸切换依赖主进程的IPC监听器
4. 登录状态保存在localStorage中，生产环境建议使用更安全的存储方式

## 扩展建议

1. **安全性增强**
   - Token刷新机制
   - 加密存储
   - HTTPS通信

2. **用户体验**
   - 登录动画
   - 错误提示优化
   - 多语言支持

3. **功能扩展**
   - 第三方登录（微信、钉钉等）
   - 生物识别登录
   - 单点登录（SSO）
