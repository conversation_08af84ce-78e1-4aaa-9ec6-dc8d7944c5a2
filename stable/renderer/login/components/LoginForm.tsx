/**
 * 基础登录表单组件
 */

import React, { useState, useCallback, useEffect } from 'react';

interface LoginFormProps {
  onSubmit: (data: LoginFormData) => void;
  error?: string;
  isLoading?: boolean;
}

export interface LoginFormData {
  username: string;
  password: string;
  rememberMe: boolean;
}

export const LoginForm: React.FC<LoginFormProps> = ({ 
  onSubmit, 
  error, 
  isLoading = false 
}) => {
  const [formData, setFormData] = useState<LoginFormData>({
    username: '',
    password: '',
    rememberMe: false
  });

  const [validationErrors, setValidationErrors] = useState<{
    username?: string;
    password?: string;
  }>({});

  // 从本地存储恢复记住的用户名
  useEffect(() => {
    const rememberMe = localStorage.getItem('rememberMe') === 'true';
    const savedUsername = localStorage.getItem('savedUsername') || '';
    
    if (rememberMe && savedUsername) {
      setFormData(prev => ({
        ...prev,
        username: savedUsername,
        rememberMe: true
      }));
    }
  }, []);

  const validateForm = useCallback((): boolean => {
    const errors: typeof validationErrors = {};
    
    if (!formData.username.trim()) {
      errors.username = '请输入用户名';
    } else if (formData.username.length < 2) {
      errors.username = '用户名至少2个字符';
    }
    
    if (!formData.password.trim()) {
      errors.password = '请输入密码';
    } else if (formData.password.length < 6) {
      errors.password = '密码至少6个字符';
    }
    
    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  }, [formData]);

  const handleInputChange = useCallback((field: keyof LoginFormData) => {
    return (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = field === 'rememberMe' ? e.target.checked : e.target.value;
      
      setFormData(prev => ({
        ...prev,
        [field]: value
      }));
      
      // 清除对应字段的验证错误
      if (validationErrors[field as keyof typeof validationErrors]) {
        setValidationErrors(prev => ({
          ...prev,
          [field]: undefined
        }));
      }
    };
  }, [validationErrors]);

  const handleSubmit = useCallback((e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    // 保存用户名（如果选择了记住我）
    if (formData.rememberMe) {
      localStorage.setItem('savedUsername', formData.username);
    } else {
      localStorage.removeItem('savedUsername');
    }
    
    onSubmit(formData);
  }, [formData, validateForm, onSubmit]);

  const handleKeyPress = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSubmit(e as any);
    }
  }, [handleSubmit]);

  return (
    <form className="login-form" onSubmit={handleSubmit}>
      <div className="form-group">
        <label htmlFor="username">用户名</label>
        <input
          id="username"
          type="text"
          value={formData.username}
          onChange={handleInputChange('username')}
          onKeyPress={handleKeyPress}
          placeholder="请输入用户名"
          disabled={isLoading}
          className={validationErrors.username ? 'error' : ''}
        />
        {validationErrors.username && (
          <span className="error-text">{validationErrors.username}</span>
        )}
      </div>

      <div className="form-group">
        <label htmlFor="password">密码</label>
        <input
          id="password"
          type="password"
          value={formData.password}
          onChange={handleInputChange('password')}
          onKeyPress={handleKeyPress}
          placeholder="请输入密码"
          disabled={isLoading}
          className={validationErrors.password ? 'error' : ''}
        />
        {validationErrors.password && (
          <span className="error-text">{validationErrors.password}</span>
        )}
      </div>

      <div className="form-group checkbox-group">
        <label className="checkbox-label">
          <input
            type="checkbox"
            checked={formData.rememberMe}
            onChange={handleInputChange('rememberMe')}
            disabled={isLoading}
          />
          <span className="checkbox-text">记住我</span>
        </label>
      </div>

      {error && (
        <div className="error-message">
          {error}
        </div>
      )}

      <button 
        type="submit" 
        className="login-btn"
        disabled={isLoading}
      >
        {isLoading ? '登录中...' : '登录'}
      </button>

      <div className="form-links">
        <a href="#" className="forgot-password">忘记密码？</a>
      </div>
    </form>
  );
};
