/**
 * 手机号验证组件
 * 预留接口，待后续实现具体的手机验证逻辑
 */

import React, { useState, useCallback, useEffect } from 'react';

interface PhoneVerifyProps {
  onSuccess: (phoneData: PhoneVerifyData) => void;
  onError: (error: string) => void;
  onCancel: () => void;
}

export interface PhoneVerifyData {
  phoneNumber: string;
  verifyCode: string;
  timestamp: number;
}

export const PhoneVerify: React.FC<PhoneVerifyProps> = ({
  onSuccess,
  onError,
  onCancel
}) => {
  const [phoneNumber, setPhoneNumber] = useState('');
  const [verifyCode, setVerifyCode] = useState('');
  const [countdown, setCountdown] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<{
    phoneNumber?: string;
    verifyCode?: string;
  }>({});

  // 倒计时效果
  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (countdown > 0) {
      timer = setTimeout(() => {
        setCountdown(countdown - 1);
      }, 1000);
    }
    return () => clearTimeout(timer);
  }, [countdown]);

  const validatePhoneNumber = (phone: string): boolean => {
    const phoneRegex = /^1[3-9]\d{9}$/;
    return phoneRegex.test(phone);
  };

  const handleSendCode = useCallback(async () => {
    if (!validatePhoneNumber(phoneNumber)) {
      setErrors({ phoneNumber: '请输入正确的手机号码' });
      return;
    }

    setIsLoading(true);
    setErrors({});

    try {
      // TODO: 调用发送验证码API
      await sendVerificationCode(phoneNumber);
      
      setCountdown(60); // 60秒倒计时
      console.log('验证码已发送到:', phoneNumber);
    } catch (error) {
      onError('发送验证码失败，请稍后重试');
    } finally {
      setIsLoading(false);
    }
  }, [phoneNumber, onError]);

  const handleVerify = useCallback(async () => {
    const newErrors: typeof errors = {};
    
    if (!validatePhoneNumber(phoneNumber)) {
      newErrors.phoneNumber = '请输入正确的手机号码';
    }
    
    if (!verifyCode.trim()) {
      newErrors.verifyCode = '请输入验证码';
    } else if (verifyCode.length !== 6) {
      newErrors.verifyCode = '验证码应为6位数字';
    }
    
    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    setIsLoading(true);
    setErrors({});

    try {
      // TODO: 调用验证API
      const isValid = await verifyPhoneCode(phoneNumber, verifyCode);
      
      if (isValid) {
        onSuccess({
          phoneNumber,
          verifyCode,
          timestamp: Date.now()
        });
      } else {
        onError('验证码错误，请重新输入');
      }
    } catch (error) {
      onError('验证失败，请稍后重试');
    } finally {
      setIsLoading(false);
    }
  }, [phoneNumber, verifyCode, onSuccess, onError]);

  // 模拟发送验证码API
  const sendVerificationCode = async (phone: string): Promise<void> => {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        // 模拟API调用
        console.log(`发送验证码到 ${phone}`);
        resolve();
      }, 1000);
    });
  };

  // 模拟验证码验证API
  const verifyPhoneCode = async (phone: string, code: string): Promise<boolean> => {
    return new Promise((resolve) => {
      setTimeout(() => {
        // 模拟验证逻辑（实际应该调用后端API）
        const isValid = code === '123456'; // 示例：固定验证码
        resolve(isValid);
      }, 1000);
    });
  };

  return (
    <div className="phone-verify">
      <div className="phone-verify-header">
        <h3>手机验证</h3>
        <p>为了确保账户安全，请验证您的手机号码</p>
      </div>
      
      <div className="phone-verify-form">
        <div className="form-group">
          <label htmlFor="phoneNumber">手机号码</label>
          <input
            id="phoneNumber"
            type="tel"
            value={phoneNumber}
            onChange={(e) => setPhoneNumber(e.target.value)}
            placeholder="请输入手机号码"
            disabled={isLoading}
            className={errors.phoneNumber ? 'error' : ''}
          />
          {errors.phoneNumber && (
            <span className="error-text">{errors.phoneNumber}</span>
          )}
        </div>

        <div className="form-group">
          <label htmlFor="verifyCode">验证码</label>
          <div className="verify-code-input">
            <input
              id="verifyCode"
              type="text"
              value={verifyCode}
              onChange={(e) => setVerifyCode(e.target.value)}
              placeholder="请输入6位验证码"
              disabled={isLoading}
              maxLength={6}
              className={errors.verifyCode ? 'error' : ''}
            />
            <button
              type="button"
              className="send-code-btn"
              onClick={handleSendCode}
              disabled={isLoading || countdown > 0 || !phoneNumber}
            >
              {countdown > 0 ? `${countdown}s` : '发送验证码'}
            </button>
          </div>
          {errors.verifyCode && (
            <span className="error-text">{errors.verifyCode}</span>
          )}
        </div>

        <div className="phone-verify-actions">
          <button
            type="button"
            className="verify-btn"
            onClick={handleVerify}
            disabled={isLoading || !phoneNumber || !verifyCode}
          >
            {isLoading ? '验证中...' : '验证'}
          </button>
          
          <button
            type="button"
            className="back-btn"
            onClick={onCancel}
            disabled={isLoading}
          >
            返回登录
          </button>
        </div>
      </div>
    </div>
  );
};
