/**
 * 登录页面逻辑
 * 这个文件包含登录页面的核心逻辑，你可以根据需要进行扩展
 */

interface LoginConfig {
  apiEndpoint?: string;
  timeout?: number;
  retryAttempts?: number;
}

class LoginManager {
  private config: LoginConfig;
  private isLoading: boolean = false;

  constructor(config: LoginConfig = {}) {
    this.config = {
      apiEndpoint: '/api/login',
      timeout: 10000,
      retryAttempts: 3,
      ...config
    };
    
    this.init();
  }

  private init() {
    // 监听登录页面就绪事件
    window.addEventListener('loginPageReady', () => {
      this.renderLoginForm();
    });
  }

  private renderLoginForm() {
    const loginContent = document.getElementById('login-content');
    if (!loginContent) return;

    loginContent.innerHTML = `
      <div class="form-group">
        <input type="text" id="username" placeholder="用户名" required />
      </div>
      <div class="form-group">
        <input type="password" id="password" placeholder="密码" required />
      </div>
      <div class="form-group">
        <label>
          <input type="checkbox" id="remember-me" />
          记住我
        </label>
      </div>
      <div id="error-message" class="error-message"></div>
    `;

    // 添加样式
    this.addFormStyles();
    
    // 绑定事件
    this.bindEvents();
  }

  private addFormStyles() {
    const style = document.createElement('style');
    style.textContent = `
      .form-group {
        margin-bottom: 20px;
      }
      
      .form-group input[type="text"],
      .form-group input[type="password"] {
        width: 100%;
        padding: 12px;
        border: 1px solid #ddd;
        border-radius: 5px;
        font-size: 14px;
        box-sizing: border-box;
      }
      
      .form-group input[type="text"]:focus,
      .form-group input[type="password"]:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 5px rgba(102, 126, 234, 0.3);
      }
      
      .form-group label {
        display: flex;
        align-items: center;
        font-size: 14px;
        color: #666;
      }
      
      .form-group input[type="checkbox"] {
        margin-right: 8px;
      }
      
      .error-message {
        color: #e74c3c;
        font-size: 14px;
        margin-top: 10px;
        min-height: 20px;
      }
      
      .loading {
        opacity: 0.6;
        pointer-events: none;
      }
    `;
    document.head.appendChild(style);
  }

  private bindEvents() {
    // 绑定回车键登录
    const usernameInput = document.getElementById('username') as HTMLInputElement;
    const passwordInput = document.getElementById('password') as HTMLInputElement;
    
    [usernameInput, passwordInput].forEach(input => {
      if (input) {
        input.addEventListener('keypress', (e) => {
          if (e.key === 'Enter') {
            this.handleLogin();
          }
        });
      }
    });

    // 重新绑定登录按钮事件
    const loginBtn = document.getElementById('login-btn');
    if (loginBtn) {
      loginBtn.onclick = () => this.handleLogin();
    }
  }

  private async handleLogin() {
    if (this.isLoading) return;

    const usernameInput = document.getElementById('username') as HTMLInputElement;
    const passwordInput = document.getElementById('password') as HTMLInputElement;
    const rememberMeInput = document.getElementById('remember-me') as HTMLInputElement;
    const errorMessage = document.getElementById('error-message');
    const loginBtn = document.getElementById('login-btn');

    if (!usernameInput || !passwordInput) return;

    const username = usernameInput.value.trim();
    const password = passwordInput.value.trim();

    // 基本验证
    if (!username || !password) {
      this.showError('请输入用户名和密码');
      return;
    }

    this.isLoading = true;
    if (loginBtn) loginBtn.textContent = '登录中...';
    if (errorMessage) errorMessage.textContent = '';
    
    // 添加加载状态
    document.getElementById('login-form')?.classList.add('loading');

    try {
      // 这里实现你的登录逻辑
      const success = await this.performLogin(username, password);
      
      if (success) {
        // 保存登录状态
        if (rememberMeInput?.checked) {
          localStorage.setItem('rememberMe', 'true');
        }
        
        // 保存用户token（示例）
        localStorage.setItem('userToken', 'your-auth-token');
        localStorage.setItem('username', username);
        
        // 登录成功，调用全局的成功处理函数
        (window as any).handleLoginSuccess();
      } else {
        this.showError('用户名或密码错误');
      }
    } catch (error) {
      console.error('Login error:', error);
      this.showError('登录失败，请稍后重试');
    } finally {
      this.isLoading = false;
      if (loginBtn) loginBtn.textContent = '登录';
      document.getElementById('login-form')?.classList.remove('loading');
    }
  }

  private async performLogin(username: string, password: string): Promise<boolean> {
    // 这里实现你的具体登录逻辑
    // 可以是API调用、本地验证等
    
    // 示例：模拟API调用
    return new Promise((resolve) => {
      setTimeout(() => {
        // 这里替换为你的实际验证逻辑
        const isValid = username === 'admin' && password === 'password';
        resolve(isValid);
      }, 1000);
    });
  }

  private showError(message: string) {
    const errorMessage = document.getElementById('error-message');
    if (errorMessage) {
      errorMessage.textContent = message;
    }
  }

  // 公共方法：检查登录状态
  public static checkLoginStatus(): boolean {
    return localStorage.getItem('userToken') !== null;
  }

  // 公共方法：登出
  public static logout() {
    localStorage.removeItem('userToken');
    localStorage.removeItem('username');
    localStorage.removeItem('rememberMe');
  }
}

// 创建登录管理器实例
const loginManager = new LoginManager();

// 暴露到全局
(window as any).LoginManager = LoginManager;
